# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - braveApiKey
    properties:
      braveApiKey:
        type: string
        description: The API key for the Brave Search server.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({command:'uv',args:['run', 'src/server.py'],env:{BRAVE_API_KEY:config.braveApiKey}})