# Storm Analysis Tool

This project analyzes rainfall data from the ASOS network for O'Hare International Airport (ORD), identifies storms based on specific criteria, categorizes them by duration, and performs machine learning clustering to identify patterns in storm behavior.

## Features

- **Data Retrieval**: Pulls all available historical rainfall data from ASOS for O'Hare (ORD) dating back to 1940
  - Checks for local files first and uses them if they cover the requested date range
  - Downloads data directly from the Iowa State ASOS network if needed
  - Processes data into hourly precipitation records
- **GPU Acceleration**: Utilizes NVIDIA GPU (if available) for faster clustering and analysis
- **Storm Identification**: Directly identifies storms of characteristic durations
  - Identifies storms of 6, 12, 18, 24, 48, and 72 hour durations
  - Minimum storm size: 0.5 inches
  - Uses rolling windows to find periods with significant rainfall
  - Ensures storm durations are appropriate for their category (within 50% of the category duration)
- **Machine Learning Clustering**: Applies various clustering techniques to identify patterns
  - K-means with Dynamic Time Warping (DTW)
  - K-means with Soft-DTW
  - Standard K-means (with optional DTW distance)
  - DBSCAN (Density-Based Spatial Clustering of Applications with Noise) (with optional DTW distance)
  - Gaussian Mixture Models (GMM) (with optional DTW distance)
  - Hierarchical Clustering (Agglomerative) (with optional DTW distance)
  - OPTICS (Ordering Points To Identify the Clustering Structure) (with optional DTW distance)
  - Spectral Clustering (with optional DTW distance)
  - HDBSCAN (Hierarchical DBSCAN, if installed) (with optional DTW distance)
- **Hyperparameter Optimization**: Finds optimal parameters for clustering algorithms
- **Visualization**: Creates comprehensive visualizations of storms and clusters
  - Actual rainfall depths
  - Unitized rainfall amounts (normalized to 0-1 range)
  - Cluster visualizations with average storm profiles

## Project Structure

```
NewCB/
├── data/                  # Directory for storing raw and processed data
├── results/               # Directory for storing analysis results and visualizations
├── logs/                  # Directory for log files
├── data_retrieval.py      # Module for retrieving ASOS rainfall data
├── storm_identification.py # Module for identifying and categorizing storms
├── storm_clustering.py    # Module for clustering analysis
├── visualization.py       # Module for creating visualizations
├── main.py                # Main script that orchestrates the workflow
└── README.md              # Project documentation
```

## Requirements

- Python 3.7+
- Required packages:
  - numpy
  - pandas
  - matplotlib
  - scikit-learn
  - tslearn
  - scipy
- Optional GPU acceleration:
  - NVIDIA GPU with CUDA support
  - cupy
  - cuml

## Installation

1. Clone the repository or download the source code
2. Install the required packages:

```bash
pip install -r requirements.txt
```

## Usage

### Paper Lookup Tools

To use the Roo Code paper lookup tools, follow these steps:

1. Start the MCP server:

```bash
python mcp_server.py
```

2. From within Roo Code, issue a tool call to one of the following methods:

- `search_arxiv(query: str)`
- `search_google_scholar(query: str)`
- `fetch_scihub(url_or_doi: str)`

These calls will return JSON-formatted results with paper metadata and citations.

### Python Documentation Tools

To use the Roo Code Python documentation tools, follow these steps:

1. Install the new dependencies:

```bash
pip install -r requirements.txt
```

2. Start the documentation server:

```bash
python mcp_python_docs_server.py
```

3. From within Roo Code, issue a tool call to one of the following methods:

- `search_devdocs(query: str)`
- `search_context7(query: str)`
- `search_official_python_docs(query: str)`

### Basic Usage

Run the main script to perform the complete analysis:

```bash
python main.py
```

This will:
1. Retrieve rainfall data from ASOS for O'Hare
2. Identify and categorize storms
3. Perform clustering analysis
4. Create visualizations

### Advanced Usage

You can customize the analysis using command-line arguments:

```bash
python main.py --start-date 1950-01-01 --end-date 2023-12-31 --storm-start-threshold 0.3 --storm-end-threshold 0.05 --min-storm-size 0.75 --fixed-clusters 5
```

#### Available Arguments

- `--start-date`: Start date for data retrieval (YYYY-MM-DD)
- `--end-date`: End date for data retrieval (YYYY-MM-DD)
- `--skip-retrieval`: Skip data retrieval step
- `--skip-identification`: Skip storm identification step
- `--skip-clustering`: Skip clustering analysis step
- `--skip-visualization`: Skip visualization step
- `--storm-start-threshold`: Rainfall threshold to define storm start (inches in 4 hours)
- `--storm-end-threshold`: Rainfall threshold to define storm end (inches in 4 hours)
- `--min-storm-size`: Minimum storm size (inches)
- `--fixed-clusters`: Fixed number of clusters for comparison
- `--use-dtw`: Use DTW distance for all clustering methods (default: True)
- `--no-dtw`: Disable DTW distance for clustering methods

### Running Individual Components

You can also run each component separately:

```bash
# Data retrieval only
python data_retrieval.py

# Storm identification only
python storm_identification.py

# Clustering analysis only
python storm_clustering.py

# Visualization only
python visualization.py
```

## Results

The analysis results are saved in the `results` directory:

- `storms.pkl`: Pickle file containing identified and categorized storms
- `*_actual_rainfall.png`: Plots of actual cumulative rainfall for each storm category
- `*_unitized_rainfall.png`: Plots of normalized cumulative rainfall for each storm category
- `*_clusters.png`: Visualizations of clusters for each storm category
- `*_clusters_centers.png`: Visualizations of cluster centers
- `*_cluster_metrics.png`: Plots of clustering evaluation metrics
- `*_hyperparameter_optimization.png`: Visualizations of hyperparameter optimization results
- `*_method_comparison.png`: Comparisons of different clustering methods
- `*_clustering_comparison.csv`: CSV file with clustering comparison metrics

## Machine Learning Techniques

This project implements several machine learning techniques for time series clustering:

1. **Dynamic Time Warping (DTW)**: A time series alignment algorithm that finds the optimal alignment between two time series by warping the time axis.

2. **Soft-DTW**: A differentiable version of DTW that allows for gradient-based optimization.

3. **DTW Distance Matrix**: A pairwise distance matrix calculated using DTW, which can be used with various clustering algorithms.

4. **K-means Clustering**: Applied with DTW and Soft-DTW distance metrics for time series clustering, as well as standard Euclidean distance.

5. **DBSCAN**: Density-based spatial clustering that can find clusters of arbitrary shape and identify noise points. Can use either Euclidean or DTW distances.

6. **Gaussian Mixture Models (GMM)**: Probabilistic models that represent data as a mixture of Gaussian distributions.

7. **Hierarchical Clustering**: Builds a hierarchy of clusters using a bottom-up (agglomerative) approach. Uses Ward linkage with Euclidean distance or average linkage with DTW distance.

8. **OPTICS**: Ordering Points To Identify the Clustering Structure, an improved version of DBSCAN that handles varying density clusters better. Can use either Euclidean or DTW distances.

9. **Spectral Clustering**: Uses eigenvalues of similarity matrices to reduce dimensionality before clustering, effective for complex, non-convex clusters. Can use DTW-based affinity matrices.

10. **HDBSCAN**: Hierarchical Density-Based Spatial Clustering of Applications with Noise, an improved version of DBSCAN that automatically determines the optimal number of clusters. Can use either Euclidean or DTW distances.

10. **Hyperparameter Optimization**: Grid search to find optimal parameters for clustering algorithms.

11. **Cluster Evaluation Metrics**:
    - Silhouette Score (Worst -1 to 1 Best): Measures how similar an object is to its own cluster compared to other clusters
    - Calinski-Harabasz Index (Higher is better): Ratio of between-cluster variance to within-cluster variance
    - Davies-Bouldin Index (Lower is better): Average similarity between clusters

## Future Improvements

Potential enhancements for future versions:

1. Implement more advanced time series clustering techniques (e.g., LSTM autoencoders)
2. Add support for other weather stations beyond O'Hare
3. Incorporate additional meteorological data (temperature, pressure, etc.)
4. Develop a web interface for interactive exploration of results
5. Implement real-time monitoring and prediction capabilities

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- ASOS network for providing precipitation data
- tslearn library for time series machine learning tools
- scikit-learn for machine learning algorithms