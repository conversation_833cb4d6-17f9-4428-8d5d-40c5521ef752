#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU Utilities Module
"""
import logging

def check_gpu_availability():
    """
    Check if a GPU is available for computation.

    Returns:
        bool: True if GPU is available via PyTorch or Numba, False otherwise.
    """
    gpu_available = False
    try:
        import torch
        if torch.cuda.is_available():
            gpu_available = True
    except ImportError:
        logging.warning("PyTorch not installed, skipping PyTorch GPU check.")
    try:
        from numba import cuda
        if cuda.is_available():
            gpu_available = True
    except ImportError:
        logging.warning("<PERSON>umba not installed, skipping Numba GPU check.")
    return gpu_available