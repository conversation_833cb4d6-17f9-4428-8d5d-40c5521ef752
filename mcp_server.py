#!/usr/bin/env python3
import sys
import json
import arxiv
from scholarly import scholarly
from scihub import SciHub
# NOTE: Consider adding specific exception handling (e.g., arxiv.arxiv.UnexpectedEmptyPageError) if needed.
# NOTE: This function performs blocking network I/O and should be considered for future async/threaded refactoring.
def search_arxiv(query: str, max_results: int = 5):
    """
    Search arXiv for papers matching the query and return metadata and a citation string.
    """
    search = arxiv.Search(query=query, max_results=max_results)
    results = []
    for entry in search.results():
        authors = [author.name for author in entry.authors]
        published = entry.published
        year = published.year if hasattr(published, 'year') else None
        arxiv_id = entry.entry_id.split('/')[-1]
        citation = f"{', '.join(authors)} ({year}). {entry.title}. arXiv:{arxiv_id}"
        results.append({
            "title": entry.title,
            "authors": authors,
            "published": published.isoformat() if hasattr(published, 'isoformat') else str(published),
            "doi": entry.doi,
            "summary": entry.summary,
            "id": arxiv_id,
            "citation": citation
        })
    return results

# NOTE: Consider adding specific exception handling (e.g., scholarly._navigator.MaxTriesExceededException) if needed.
# NOTE: This function performs blocking network I/O and should be considered for future async/threaded refactoring.
def search_google_scholar(query: str, max_results: int = 5):
    """
    Search Google Scholar for papers matching the query and return metadata and citation counts.
    """
    results = []
    search_query = scholarly.search_pubs(query)
    for _ in range(max_results):
        try:
            pub = next(search_query)
            paper = scholarly.fill(pub)
            bib = paper.get("bib", {})
            results.append({
                "title": bib.get("title"),
                "authors": bib.get("author"),
                "year": bib.get("pub_year"),
                "abstract": bib.get("abstract"),
                "num_citations": paper.get("num_citations"),
                "url": bib.get("url")
            })
        except StopIteration:
            break
    return results

# WARNING: Sci-Hub usage may be unreliable and raises legal/ethical concerns.
# NOTE: This function performs blocking network I/O and should be considered for future async/threaded refactoring.
def fetch_scihub(url_or_doi: str):
    """
    Retrieve a PDF URL or metadata from Sci-Hub for the given DOI or URL.
    """
    sh = SciHub()
    try:
        # Attempt to get PDF URL via SciHub library
        if hasattr(sh, "get_pdf_url"):
            pdf_url = sh.get_pdf_url(url_or_doi)
        else:
            # Fallback to Sci-Hub page URL
            pdf_url = f"https://sci-hub.se/{url_or_doi}"
        return {"pdf_url": pdf_url}
    except Exception as e: # TODO: Consider more specific exceptions if the library provides them.
        # Log the error for server-side diagnostics (optional)
        # print(f"SciHub Error: {e}", file=sys.stderr)
        return {"error": f"Failed to fetch from Sci-Hub: {str(e)}"}

# Map JSON-RPC methods to functions
TOOLS = {
    "search_arxiv": search_arxiv,
    "search_google_scholar": search_google_scholar,
    "fetch_scihub": fetch_scihub
}

def main():
    """
    Simple JSON-RPC over STDIO:
    Reads requests with 'method' and 'params' from stdin, dispatches to the tool,
    and writes JSON responses with a 'result' field to stdout.
    """
    while True:
        line = sys.stdin.readline()
        if not line:
            break
        request_id = None # Initialize request_id
        try:
            request = json.loads(line)
            request_id = request.get("id") # Capture request ID
            method = request.get("method")
            params = request.get("params", {}) or {} # Ensure params is a dict

            response = {"jsonrpc": "2.0", "id": request_id} # Base response

            if method == "getTools":
                response["result"] = list(TOOLS.keys())
            elif method in TOOLS:
                result = TOOLS[method](**params)
                # Check if the tool returned an error itself (like fetch_scihub)
                if isinstance(result, dict) and "error" in result:
                     response["error"] = {"code": -32000, "message": result["error"]}
                else:
                    response["result"] = result
            elif method:
                 response["error"] = {"code": -32601, "message": f"Method not found: {method}"}
            else:
                 response["error"] = {"code": -32600, "message": "Invalid Request: No method specified"}

        except json.JSONDecodeError as e:
             response = {"jsonrpc": "2.0", "error": {"code": -32700, "message": f"Parse error: {str(e)}"}, "id": None}
        except Exception as e:
            # Ensure response is always defined, even if request parsing failed partially
            if 'response' not in locals():
                 response = {"jsonrpc": "2.0", "id": request_id} # Use captured ID if available
            response["error"] = {"code": -32000, "message": f"Server error: {str(e)}"}

        sys.stdout.write(json.dumps(response) + "\n")
        sys.stdout.flush()

if __name__ == "__main__":
    main()