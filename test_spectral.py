#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for spectral clustering
"""

import numpy as np
from sklearn.cluster import SpectralClustering

# Create some dummy data
data = np.random.rand(10, 5)
distance_matrix = np.random.rand(10, 10)

# Make the distance matrix symmetric
distance_matrix = (distance_matrix + distance_matrix.T) / 2
np.fill_diagonal(distance_matrix, 0)

# Convert distances to similarities
sigma = np.median(distance_matrix)
affinity_matrix = np.exp(-distance_matrix / (2 * sigma**2))

print("Testing SpectralClustering with affinity='precomputed'...")
try:
    # Test with affinity='precomputed'
    model = SpectralClustering(
        n_clusters=2,
        random_state=42,
        affinity='precomputed'
    )
    labels = model.fit_predict(affinity_matrix)
    print("Success! Labels:", labels)
except Exception as e:
    print(f"Error with affinity='precomputed': {e}")

print("\nTest complete.")