FROM python:3.10-slim

WORKDIR /app

COPY pyproject.toml run_server.py ./
COPY mcp_sequential_thinking ./mcp_sequential_thinking

# Create a dummy README.md file to satisfy the package metadata requirements
RUN echo "# MCP Sequential Thinking" > README.md

# Install portalocker and other dependencies explicitly
RUN pip install --no-cache-dir portalocker
RUN pip install --no-cache-dir .

EXPOSE 8000

CMD ["python", "run_server.py"]