#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for hierarchical clustering with real data
"""

import os
import pickle
import numpy as np
from sklearn.cluster import AgglomerativeClustering
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Path to the storms pickle file
storms_path = os.path.join('results', 'storms.pkl')

# Try to load the storms data
try:
    with open(storms_path, 'rb') as f:
        storms = pickle.load(f)
    logger.info(f"Loaded storms from {storms_path}")
    
    # Get the first category of storms
    category = list(storms.keys())[0]
    storm_list = storms[category]
    logger.info(f"Using category {category} with {len(storm_list)} storms")
    
    # Create a simple matrix of time series data
    # This is just a placeholder - in the real code, this would be the actual storm data
    data = np.random.rand(len(storm_list), 10)
    
    # Calculate a distance matrix (just for testing)
    from scipy.spatial.distance import pdist, squareform
    distance_matrix = squareform(pdist(data))
    
    logger.info("Testing hierarchical clustering with metric='precomputed'...")
    try:
        # Test with metric='precomputed'
        model = AgglomerativeClustering(
            n_clusters=2,
            metric='precomputed',
            linkage='average'
        )
        labels = model.fit_predict(distance_matrix)
        logger.info(f"Success! Found {len(set(labels))} clusters")
    except Exception as e:
        logger.error(f"Error with metric='precomputed': {e}")
        
        # Try with affinity='precomputed' as a fallback
        logger.info("Trying with affinity='precomputed' instead...")
        try:
            model = AgglomerativeClustering(
                n_clusters=2,
                affinity='precomputed',
                linkage='average'
            )
            labels = model.fit_predict(distance_matrix)
            logger.info(f"Success! Found {len(set(labels))} clusters")
        except Exception as e:
            logger.error(f"Error with affinity='precomputed': {e}")
    
    logger.info("Test complete.")
    
except Exception as e:
    logger.error(f"Error loading storms: {e}")