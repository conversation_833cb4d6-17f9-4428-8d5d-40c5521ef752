import os
import pandas as pd
import logging
from tqdm import tqdm

logger = logging.getLogger(__name__)

class StormIdentifier:
    def __init__(self, data_path, output_path):
        self.data_path = data_path
        self.output_path = output_path
        self.min_duration = 22  # hours
        self.max_duration = 26  # hours
        self.min_depth = 0.5  # inches

    def load_data(self):
        """Loads the data from the CSV file into a pandas DataFrame."""
        try:
            df = pd.read_csv(self.data_path, parse_dates=['datetime'])
            return df
        except FileNotFoundError:
            logger.error(f"File not found: {self.data_path}")
            return None
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return None

    def identify_storms(self, df, max_records=None):
        """
        Identifies 24-hour storms based on the duration and depth criteria.
        
        Args:
            df: DataFrame with precipitation data
            max_records: Optional limit on number of records to process (for testing)
        
        Returns:
            List of storm dictionaries
        """
        storms = []
        
        # Limit records if specified (useful for testing)
        if max_records is not None and max_records > 0:
            df = df.iloc[:max_records].copy()
        
        # Reset index to ensure consistent indexing
        df = df.reset_index(drop=True)
        
        # Create a sliding window approach for better performance
        total_records = len(df)
        logger.info(f"Processing {total_records} records to identify storms")
        
        with tqdm(total=total_records, desc="Identifying storms") as pbar:
            i = 0
            while i < total_records - 1:
                start_index = i
                start_time = df.iloc[start_index]['datetime']
                
                # Find all records within the maximum duration window
                # This is more efficient than checking each record individually
                end_indices = df.index[(df['datetime'] >= start_time) &
                                      (df['datetime'] <= start_time + pd.Timedelta(hours=self.max_duration))]
                
                if len(end_indices) == 0:
                    i += 1
                    pbar.update(1)
                    continue
                
                # Get the last index in the window
                end_index = end_indices[-1]
                end_time = df.iloc[end_index]['datetime']
                
                # Calculate duration in hours
                duration = (end_time - start_time).total_seconds() / 3600
                
                # Calculate total precipitation in the window
                window_df = df.iloc[start_index:end_index+1]
                total_depth = window_df['precipitation'].sum()
                
                # Check if the storm meets the criteria
                if self.min_duration <= duration <= self.max_duration and total_depth >= self.min_depth:
                    # Get lat and lon from the start index
                    lat = df.iloc[start_index]['lat']
                    lon = df.iloc[start_index]['lon']
                    
                    storms.append({
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration_hours': duration,
                        'total_depth_inches': total_depth,
                        'lat': lat,
                        'lon': lon
                    })
                    
                    # Move the index to the end of the storm
                    i = end_index + 1
                else:
                    # Move to the next hour
                    i += 1
                
                pbar.update(i - start_index)
        
        logger.info(f"Identified {len(storms)} storms with duration between {self.min_duration} and {self.max_duration} hours and minimum depth of {self.min_depth} inches")
        return storms

    def save_storms(self, storms):
        """Saves the identified storms to a CSV file in the results/ directory."""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            
            # Convert to DataFrame and save
            df = pd.DataFrame(storms)
            
            # Format datetime columns for better readability
            if 'start_time' in df.columns:
                df['start_time'] = df['start_time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            if 'end_time' in df.columns:
                df['end_time'] = df['end_time'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Save to CSV
            df.to_csv(self.output_path, index=False)
            logger.info(f"Saved {len(storms)} identified storms to {self.output_path}")
            return self.output_path
        except Exception as e:
            logger.error(f"Error saving storms: {e}")
            return None

    def run(self, max_records=None):
        """
        Run the complete storm identification process.
        
        Args:
            max_records: Optional limit on number of records to process (for testing)
        
        Returns:
            Path to the saved storms CSV file
        """
        logger.info("Starting storm identification process")
        
        # Load data
        df = self.load_data()
        if df is None:
            logger.error("Failed to load data")
            return None
        
        # Identify storms
        storms = self.identify_storms(df, max_records=max_records)
        
        # Save storms
        output_path = self.save_storms(storms)
        
        return output_path