#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for storm identification

This script tests the StormIdentifier class to ensure it correctly identifies
24-hour storms from the precipitation data.
"""

import os
import logging
import pandas as pd
from storm_identification import StormIdentifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to test storm identification."""
    # Define paths
    data_path = os.path.join('data', 'processed_precip_data.csv')
    output_path = os.path.join('results', 'test_identified_storms.csv')
    
    # Create StormIdentifier instance
    storm_identifier = StormIdentifier(data_path, output_path)
    
    # Run the complete process on the full dataset
    result_path = storm_identifier.run()
    
    if result_path:
        # Check if the file exists and has content
        try:
            storms_df = pd.read_csv(result_path)
            storm_count = len(storms_df)
            print(f"\nSuccessfully identified {storm_count} storms")
            
            if storm_count > 0:
                print("\nFirst 5 identified storms:")
                print(storms_df.head())
                
                # Calculate some statistics
                avg_duration = storms_df['duration_hours'].mean()
                avg_depth = storms_df['total_depth_inches'].mean()
                max_depth = storms_df['total_depth_inches'].max()
                
                print(f"\nStatistics:")
                print(f"Average storm duration: {avg_duration:.2f} hours")
                print(f"Average storm depth: {avg_depth:.2f} inches")
                print(f"Maximum storm depth: {max_depth:.2f} inches")
            else:
                print("\nNo storms were found that meet the criteria.")
                print("Criteria: Duration between 22-26 hours and minimum depth of 0.5 inches")
        except pd.errors.EmptyDataError:
            print("\nNo storms were found that meet the criteria.")
            print("Criteria: Duration between 22-26 hours and minimum depth of 0.5 inches")
    else:
        print("Failed to identify storms")

if __name__ == "__main__":
    main()