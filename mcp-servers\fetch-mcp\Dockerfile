# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./

# Install pnpm and shx (missing dependency)
RUN npm install -g pnpm shx

# Install dependencies without running lifecycle scripts
RUN pnpm install --ignore-scripts

# Copy source code
COPY tsconfig.json jest.config.js ./
COPY src ./src

# Build manually
RUN pnpm exec tsc

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy built files and dependencies from build stage
COPY --from=build /app/dist ./dist
COPY --from=build /app/node_modules ./node_modules
COPY package.json ./

EXPOSE 3000

CMD ["node", "dist/index.js"]