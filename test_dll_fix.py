#!/usr/bin/env python3
"""Test CuPy with explicit DLL directory"""

import os
import sys

def test_with_dll_directory():
    """Test CuPy with explicit DLL directory"""
    try:
        # Add CUDA 12.9 bin directory to DLL search path
        cuda_bin = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin"
        if os.path.exists(cuda_bin):
            os.add_dll_directory(cuda_bin)
            print(f"✓ Added DLL directory: {cuda_bin}")
        
        # Also add lib directory if it exists
        cuda_lib = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64"
        if os.path.exists(cuda_lib):
            os.add_dll_directory(cuda_lib)
            print(f"✓ Added DLL directory: {cuda_lib}")
        
        # Now try to import CuPy
        import cupy as cp
        print("✓ CuPy imported successfully")
        
        # Test basic operations
        test_array = cp.array([1, 2, 3])
        print(f"✓ Basic operations work: {test_array}")
        
        # Test NVRTC
        test_kernel = cp.RawKernel(r'''
        extern "C" __global__
        void test_kernel() {
            // Simple test kernel
        }
        ''', 'test_kernel')
        
        print("✓ NVRTC kernel compilation successful!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("Testing CuPy with explicit DLL directories...")
    print("=" * 50)
    
    success = test_with_dll_directory()
    
    if success:
        print("\n🎉 SUCCESS! CuPy with NVRTC is working!")
    else:
        print("\n❌ Still not working. Need alternative solution.")
    
    sys.exit(0 if success else 1)