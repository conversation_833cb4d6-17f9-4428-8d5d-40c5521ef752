# Storm Identification Plan

## 1. Introduction

This document outlines the plan for identifying 24-hour storms in a dataset of hourly rainfall data. The goal is to identify storms that meet specific criteria (duration and depth) and save them to a CSV file for review.

## 2. Data

The data is located in `data/processed_precip_data.csv` and contains hourly rainfall data. The file has the following columns:

*   `datetime`: Date and time of the measurement
*   `precipitation`: Rainfall amount in inches
*   `lat`: Latitude
*   `lon`: Longitude

## 3. Requirements

*   Identify storms with a duration between 22 and 26 hours (inclusive).
*   The minimum total precipitation depth for a storm to be considered valid is 0.5 inches.
*   Add a progress bar to the storm identification process.

## 4. Plan

1.  **Create `StormIdentifier` class in `storm_identification.py`:**

    *   Implement methods for:
        *   Loading data from the CSV file.
        *   Identifying 24-hour storms based on the duration and depth criteria.
        *   Saving the identified storms to a CSV file in the `results/` directory.
        *   Adding a progress bar to the storm identification process.
2.  **Modify `identify_storms` function in `main.py`:**

    *   Instantiate the new `StormIdentifier` class.
    *   Call the methods to load data, identify storms, and save the results to a CSV file.
    *   Remove the existing storm identification logic.
3.  **Add command-line argument:**

    *   Add a new command-line argument to `main.py` to allow users to specify the output file path for the identified 24-hour storms.
4.  **Update `setup_directories` function:**

    *   Ensure the `results` directory is created if it doesn't exist.

## 5. Implementation Details

### 5.1. `StormIdentifier` Class

The `StormIdentifier` class will be responsible for loading the data, identifying storms, and saving the results. It will have the following methods:

*   `__init__(self, data_path, output_path)`: Initializes the class with the data path and output path.
*   `load_data(self)`: Loads the data from the CSV file into a pandas DataFrame.
*   `identify_storms(self)`: Iterates through the data, identifies potential storms, and checks if they meet the defined criteria.
*   `save_storms(self, storms)`: Saves the identified storms to a CSV file in the `results/` directory.
*   `add_progress_bar(self)`: Adds a progress bar to the storm identification process.

### 5.2. `identify_storms` Function in `main.py`

The `identify_storms` function in `main.py` will be modified to use the new `StormIdentifier` class. It will:

1.  Instantiate the `StormIdentifier` class with the data path and output path.
2.  Call the `load_data()` method to load the data.
3.  Call the `identify_storms()` method to identify the storms.
4.  Call the `save_storms()` method to save the results to a CSV file.

### 5.3. Command-Line Argument

A new command-line argument will be added to `main.py` to allow users to specify the output file path for the identified 24-hour storms. This argument will be optional and will default to `results/identified_storms.csv` if not specified.

### 5.4. `setup_directories` Function

The `setup_directories` function will be updated to ensure that the `results` directory is created if it doesn't exist.

## 6. Output

The identified storms will be saved to a CSV file in the `results/` directory. The file will have the following columns:

*   `start_time`: Start time of the storm
*   `end_time`: End time of the storm
*   `duration_hours`: Duration of the storm in hours
*   `total_depth_inches`: Total precipitation depth in inches
*   `lat`: Latitude of the storm
*   `lon`: Longitude of the storm

## 7. Next Steps

1.  Implement the `StormIdentifier` class in `storm_identification.py`.
2.  Modify the `identify_storms` function in `main.py`.
3.  Add the command-line argument to `main.py`.
4.  Update the `setup_directories` function.
5.  Test the code with a subset of the data to ensure it correctly identifies storms.
6.  Integrate the code with `main.py` and run the entire workflow.