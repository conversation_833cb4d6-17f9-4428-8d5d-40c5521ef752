#!/usr/bin/env python3

import sys
import json
import logging
import httpx  # Replaced requests with httpx
from bs4 import BeautifulSoup

# Define constants for URLs
DEVDOCS_BASE_URL = "https://devdocs.io/#q={query}"
CONTEXT7_API_URL = "https://api.context7.com/search"
PYTHON_DOCS_SEARCH_URL = "https://docs.python.org/3/search.html"
PYTHON_DOCS_BASE_URL = "https://docs.python.org/3/"

def search_devdocs(query):
    """
    Search DevDocs for the given query and return relevant snippets and links.
    """
    # Placeholder implementation using DevDocs URL
    url = DEVDOCS_BASE_URL.format(query=query) # Use constant
    return [{"title": f"DevDocs search for {query}", "url": url, "snippet": ""}]

def search_context7(query):
    """
    Search Context7 documentation via its API and return relevant content.
    """
    params = {"q": query}
    results = []
    try:
        response = httpx.get(CONTEXT7_API_URL, params=params) # Use constant and httpx
        response.raise_for_status()  # Raise HTTPStatusError for bad responses (4xx or 5xx)
        data = response.json()
        for item in data.get("results", []):
            results.append({
                "title": item.get("title"),
                "url": item.get("url"),
                "snippet": item.get("snippet")
            })
    except httpx.RequestError as exc:
        logging.error(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        logging.error(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")
    except json.JSONDecodeError:
        logging.error(f"Failed to decode JSON response from {CONTEXT7_API_URL}")
    return results

def search_official_python_docs(query):
    """
    Fetch documentation from docs.python.org for the query, returning signatures and descriptions.
    """
    params = {"q": query, "check_keywords": "yes", "area": "default"}
    results = []
    try:
        response = httpx.get(PYTHON_DOCS_SEARCH_URL, params=params) # Use constant and httpx
        response.raise_for_status() # Raise HTTPStatusError for bad responses (4xx or 5xx)
        soup = BeautifulSoup(response.text, "html.parser")
        # Parse search results
        for li in soup.select("li.search-result"):
            a_tag = li.find("a", href=True)
            snippet_tag = li.find("p")
            title = a_tag.text.strip() if a_tag else ""
            url = PYTHON_DOCS_BASE_URL + a_tag["href"] if a_tag and a_tag["href"] else "" # Use constant
            snippet = snippet_tag.text.strip() if snippet_tag else ""
            results.append({"title": title, "url": url, "snippet": snippet})
    except httpx.RequestError as exc:
        logging.error(f"An error occurred while requesting {exc.request.url!r}: {exc}")
    except httpx.HTTPStatusError as exc:
        logging.error(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text}")
    return results

def handle_request(request):
    method = request.get("method")
    params = request.get("params") or {}
    query = params.get("query")
    if method in ("getTools", "listTools"):
        return {"result": ["search_devdocs", "search_context7", "search_official_python_docs"]}
    elif method == "search_devdocs":
        result = search_devdocs(query)
    elif method == "search_context7":
        result = search_context7(query)
    elif method == "search_official_python_docs":
        result = search_official_python_docs(query)
    else:
        return {"error": {"code": -32601, "message": "Method not found"}}
    return {"result": result}

def main():
    logging.basicConfig(level=logging.INFO)
    while True:
        line = sys.stdin.readline()
        if not line:
            break
        try:
            request = json.loads(line)
            req_id = request.get("id")
            method = request.get("method")
            if method in ("getTools", "listTools"):
                result = ["search_devdocs", "search_context7", "search_official_python_docs"]
                response = {"result": result}
            else:
                response = handle_request(request)
        except Exception as e:
            response = {"error": {"code": -32603, "message": str(e)}}
        if req_id is not None:
            response["id"] = req_id
        response["jsonrpc"] = "2.0"
        sys.stdout.write(json.dumps(response) + "\n")
        sys.stdout.flush()


# Removed duplicate main guard