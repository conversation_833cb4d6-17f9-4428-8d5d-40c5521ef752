#!/usr/bin/env python3
"""Test CuPy NVRTC functionality"""

import cupy as cp
import sys

def test_cupy_nvrtc():
    """Test if CuPy can use NVRTC for kernel compilation"""
    try:
        print("Testing CuPy with NVRTC...")
        
        # Test basic CuPy functionality
        test_array = cp.array([1, 2, 3])
        print(f"✓ Basic CuPy operations work: {test_array}")
        
        # Test NVRTC kernel compilation
        test_kernel = cp.RawKernel(r'''
        extern "C" __global__
        void test_kernel(float* data, int n) {
            int idx = blockIdx.x * blockDim.x + threadIdx.x;
            if (idx < n) {
                data[idx] = data[idx] * 2.0f;
            }
        }
        ''', 'test_kernel')
        
        # Test the kernel
        data = cp.array([1.0, 2.0, 3.0, 4.0], dtype=cp.float32)
        test_kernel((1,), (4,), (data, len(data)))
        result = cp.asnumpy(data)
        
        print(f"✓ NVRTC kernel compilation successful!")
        print(f"✓ Kernel execution result: {result}")
        print("✓ CuPy with NVRTC is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"✗ NVRTC test failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False

def test_cupy_basic():
    """Test basic CuPy operations without NVRTC"""
    try:
        print("\nTesting basic CuPy operations...")
        
        # Basic array operations
        a = cp.array([1, 2, 3, 4, 5])
        b = cp.array([2, 3, 4, 5, 6])
        c = a + b
        
        print(f"✓ Array addition: {cp.asnumpy(c)}")
        
        # Matrix operations
        matrix = cp.random.random((100, 100))
        result = cp.dot(matrix, matrix.T)
        
        print(f"✓ Matrix multiplication: {result.shape}")
        print("✓ Basic CuPy operations work!")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic CuPy test failed: {e}")
        return False

if __name__ == "__main__":
    print("CuPy NVRTC Diagnostic Test")
    print("=" * 40)
    
    # Test basic CuPy first
    basic_works = test_cupy_basic()
    
    # Test NVRTC functionality
    nvrtc_works = test_cupy_nvrtc()
    
    print("\n" + "=" * 40)
    print("SUMMARY:")
    print(f"Basic CuPy: {'✓ WORKING' if basic_works else '✗ FAILED'}")
    print(f"NVRTC Support: {'✓ WORKING' if nvrtc_works else '✗ FAILED'}")
    
    if nvrtc_works:
        print("\n🎉 Your CuPy installation is fully functional!")
        print("GPU acceleration with custom kernels will work.")
    elif basic_works:
        print("\n⚠️  CuPy basic operations work, but NVRTC is not available.")
        print("GPU acceleration will work for basic operations only.")
    else:
        print("\n❌ CuPy is not working properly.")
        print("GPU acceleration is not available.")
    
    sys.exit(0 if nvrtc_works else 1)