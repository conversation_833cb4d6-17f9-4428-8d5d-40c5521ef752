#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Storm Clustering Analysis

This script performs time series clustering on storm event data using various methods:
- K-means with Dynamic Time Warping (DTW)
- K-means with Soft-DTW
- DBSCAN
- Gaussian Mixture Models

It also performs hyperparameter optimization and cluster analysis.

Author: AI Assistant
"""

import os
import numpy as np
import pandas as pd
import pickle
import matplotlib.pyplot as plt
import matplotlib.ticker as mtick
import matplotlib.cm as cm
from matplotlib.colors import to_rgba
import datetime as dt
from scipy.interpolate import interp1d
from scipy import stats
import logging
from collections import defaultdict
import platform

# Fix for Windows CUDA DLL loading issues
if platform.system() == "Windows":
    # Add CUDA directories to DLL search path for CuPy NVRTC support
    cuda_paths = [
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\lib\x64",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\bin",
        r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\lib\x64"
    ]
    
    for cuda_path in cuda_paths:
        if os.path.exists(cuda_path):
            try:
                os.add_dll_directory(cuda_path)
                logging.info(f"Added CUDA DLL directory: {cuda_path}")
            except (OSError, AttributeError) as e:
                logging.debug(f"Could not add DLL directory {cuda_path}: {e}")

# Machine learning imports
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering, OPTICS, SpectralClustering
from sklearn.mixture import GaussianMixture
from sklearn.metrics import (
    silhouette_score, silhouette_samples, calinski_harabasz_score, davies_bouldin_score,
    accuracy_score, rand_score, adjusted_rand_score, mutual_info_score,
    normalized_mutual_info_score, adjusted_mutual_info_score
)
from sklearn.model_selection import GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.neighbors import NearestNeighbors

# Add imports for aeon distances
from aeon.distances import (
    dtw_distance, ddtw_distance, wdtw_distance, wddtw_distance,
    lcss_distance, edr_distance, erp_distance, msm_distance, twe_distance,
    euclidean_distance, pairwise_distance, msm_pairwise_distance
)
import warnings

# Try to import HDBSCAN
try:
    import hdbscan
    HDBSCAN_AVAILABLE = True
except ImportError:
    HDBSCAN_AVAILABLE = False
    warnings.warn("HDBSCAN package not found. HDBSCAN clustering will not be available.")
    
# Time series specific imports
# Replaced tslearn with aeon equivalents
try:
    # Try to import aeon preprocessing (if available)
    from aeon.transformations.series import Normalizer
    AEON_PREPROCESSING_AVAILABLE = True
except ImportError:
    # Fall back to tslearn for preprocessing if aeon doesn't have it
    try:
        from tslearn.preprocessing import TimeSeriesScalerMeanVariance, TimeSeriesScalerMinMax
        AEON_PREPROCESSING_AVAILABLE = False
    except ImportError:
        AEON_PREPROCESSING_AVAILABLE = False

# Import aeon distance functions
try:
    from aeon.distances import dtw_distance as aeon_dtw
    AEON_DTW_AVAILABLE = True
except ImportError:
    # Fall back to tslearn for DTW if needed
    try:
        from tslearn.metrics import dtw, soft_dtw, cdist_dtw
        AEON_DTW_AVAILABLE = False
    except ImportError:
        AEON_DTW_AVAILABLE = False

# aeon imports for clustering - replacing tslearn with aeon where possible
from aeon.clustering import TimeSeriesKMeans
from aeon.clustering import TimeSeriesKShape as KShape
from aeon.clustering import TimeSeriesKMedoids
from aeon.clustering import KSpectralCentroid

# Feature-based clustering functionality
# Note: TimeSeriesFeatureExtractor and Catch22 are not available in aeon 1.1.0
# We'll implement a basic feature extractor using numpy

# Try to import CuPy for GPU acceleration
try:
    import cupy as cp
    USE_GPU = True
    logging.info("CuPy imported successfully. GPU acceleration is available.")
except ImportError:
    USE_GPU = False
    logging.warning("CuPy package not found. GPU acceleration will not be available.")

if USE_GPU:
    # Define CUDA kernel for DTW
    dtw_kernel = cp.RawKernel(r'''
        extern "C" __global__
        void dtw_cuda(const float *series1, const float *series2, float *result, int len) {
            __shared__ float cost_matrix[100][100]; // Assuming max length of 100 for time series
            int idx = threadIdx.x;
            if (idx >= len) return;

            // Initialize the first row and column of the cost matrix
            if (threadIdx.x == 0) {
                cost_matrix[0][0] = abs(series1[0] - series2[0]);
                for (int i = 1; i < len; i++) {
                    cost_matrix[i][0] = cost_matrix[i-1][0] + abs(series1[i] - series2[0]);
                    cost_matrix[0][i] = cost_matrix[0][i-1] + abs(series1[0] - series2[i]);
                }
            }
            __syncthreads();

            // Calculate the rest of the cost matrix
            for (int i = 1; i < len; i++) {
                for (int j = 1; j < len; j++) {
                    float cost = abs(series1[i] - series2[j]);
                    cost_matrix[i][j] = cost + min(min(cost_matrix[i-1][j], cost_matrix[i][j-1]), cost_matrix[i-1][j-1]);
                }
            }
            __syncthreads();

            // Store the result
            if (threadIdx.x == 0) {
                result[0] = cost_matrix[len-1][len-1];
            }
        }
    ''', 'dtw_cuda')

# Check for GPU availability (Windows-compatible)
USE_GPU = False
try:
    import cupy as cp
    # Test if CuPy can actually access the GPU
    cp.cuda.Device(0).use()
    test_array = cp.array([1, 2, 3])
    USE_GPU = True
    logging.info("GPU acceleration enabled using CuPy with NVIDIA GPU")
except (ImportError, cp.cuda.runtime.CUDARuntimeError) as e:
    logging.warning(f"GPU acceleration not available. Using CPU only. Error: {e}")

# Set environment variable to suppress KMeans memory leak warning on Windows
import os
if platform.system() == "Windows":
    os.environ['OMP_NUM_THREADS'] = '1'

# Set number of CPU cores to use
N_JOBS = 12  # Use up to 12 CPU cores for parallel operations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def monitor_gpu_performance():
    """
    Monitor GPU performance and memory usage.
    
    Returns:
    --------
    dict
        Dictionary containing GPU performance metrics
    """
    if not USE_GPU:
        return {"gpu_available": False}
    
    try:
        # Get GPU memory info
        mempool = cp.get_default_memory_pool()
        used_bytes = mempool.used_bytes()
        total_bytes = mempool.total_bytes()
        
        # Get GPU device info
        device = cp.cuda.Device()
        device_name = cp.cuda.runtime.getDeviceProperties(device.id)['name'].decode('utf-8')
        compute_capability = device.compute_capability
        
        # Calculate memory usage percentage
        memory_usage = (used_bytes / total_bytes * 100) if total_bytes > 0 else 0
        
        gpu_info = {
            "gpu_available": True,
            "device_name": device_name,
            "compute_capability": f"{compute_capability[0]}.{compute_capability[1]}",
            "memory_used_mb": used_bytes / (1024**2),
            "memory_total_mb": total_bytes / (1024**2),
            "memory_usage_percent": memory_usage
        }
        
        logger.info(f"GPU Status: {device_name}, Memory: {memory_usage:.1f}% used")
        return gpu_info
        
    except Exception as e:
        logger.warning(f"Error monitoring GPU performance: {e}")
        return {"gpu_available": False, "error": str(e)}

def optimize_gpu_memory():
    """
    Optimize GPU memory usage by clearing unused memory pools.
    """
    if USE_GPU:
        try:
            # Clear memory pool
            mempool = cp.get_default_memory_pool()
            mempool.free_all_blocks()

            # Clear pinned memory pool
            pinned_mempool = cp.get_default_pinned_memory_pool()
            pinned_mempool.free_all_blocks()

            logger.info("GPU memory optimized")
        except Exception as e:
            logger.warning(f"Error optimizing GPU memory: {e}")

def gpu_euclidean_distance_matrix(data):
    """
    Calculate Euclidean distance matrix using GPU acceleration.

    Parameters:
    -----------
    data : numpy.ndarray
        Input data matrix

    Returns:
    --------
    numpy.ndarray
        Distance matrix
    """
    if not USE_GPU:
        return None

    try:
        # Convert to GPU
        gpu_data = cp.asarray(data, dtype=cp.float32)
        n_samples = gpu_data.shape[0]

        # Calculate pairwise distances using broadcasting
        # ||a - b||^2 = ||a||^2 + ||b||^2 - 2*a*b
        norms = cp.sum(gpu_data**2, axis=1, keepdims=True)
        distances_sq = norms + norms.T - 2 * cp.dot(gpu_data, gpu_data.T)

        # Take square root and ensure non-negative
        distances = cp.sqrt(cp.maximum(distances_sq, 0))

        # Convert back to CPU
        result = cp.asnumpy(distances)

        # Clear GPU memory
        del gpu_data, norms, distances_sq, distances
        cp.get_default_memory_pool().free_all_blocks()

        return result

    except Exception as e:
        logger.warning(f"GPU distance calculation failed: {e}")
        return None

class StormClusterer:
    """Class to perform clustering analysis on storm data."""

    def __init__(self, storms_path=None, output_dir='results'):
        """
        Initialize the storm clusterer.

        Parameters:
        -----------
        storms_path : str
            Path to the pickle file containing storm data.
        output_dir : str
            Directory to store results.
        """
        self.storms_path = storms_path
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)

        # Clustering parameters
        self.n_clusters_range = range(2, 11)  # Range of cluster numbers to try
        self.fixed_n_clusters = 4  # Fixed number of clusters for comparison
        self.resolution = 100  # Number of points to use for interpolation

        # Elastic distance measures from the paper
        # Prioritizing MSM distance based on research findings
        # Research shows that distance measures that employ editing in conjunction with warping (like MSM and TWE)
        # perform significantly better than other approaches
        self.elastic_distances = {
            'MSM': msm_distance,  # Prioritized based on research findings
            'TWE': twe_distance,  # Also performs well according to research
            'Euclidean': euclidean_distance,
            'DTW': dtw_distance,  # Research indicates DTW is not better than Euclidean
            'DDTW': ddtw_distance,
            'WDTW': wdtw_distance,
            'WDDTW': wddtw_distance,
            'LCSS': lcss_distance,
            'EDR': edr_distance,
            'ERP': erp_distance
        }
        
        # Feature extraction methods for feature-based clustering
        # Using basic numpy functions instead of Catch22 and TimeSeriesFeatureExtractor
        self.feature_extractors = {
            'basic_stats': {
                'functions': [np.mean, np.std, np.min, np.max, np.median,
                             np.var, np.ptp, lambda x: np.quantile(x, 0.25),
                             lambda x: np.quantile(x, 0.75)]
            }
        }

        # Initialize data structures
        self.storms = None
        self.categorized_storms = None
        self.processed_data = {}
        self.clustering_results = {}

        # Hyperparameter storage
        self.hyperparams_file = os.path.join(output_dir, 'hyperparameters.pkl')
        self.best_hyperparams = {}

        # Load previously saved hyperparameters if they exist
        self.load_hyperparameters()

        # Monitor GPU performance at initialization
        self.gpu_info = monitor_gpu_performance()
        if self.gpu_info["gpu_available"]:
            logger.info(f"GPU acceleration enabled: {self.gpu_info['device_name']}")
            logger.info(f"GPU memory available: {self.gpu_info['memory_total_mb']:.1f} MB")
        else:
            logger.info("GPU acceleration not available, using CPU only")

        logger.info("Initialized Storm Clusterer")

    def save_hyperparameters(self):
        """
        Save the best hyperparameters to a file.
        """
        try:
            with open(self.hyperparams_file, 'wb') as f:
                pickle.dump(self.best_hyperparams, f)
            logger.info(f"Saved hyperparameters to {self.hyperparams_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving hyperparameters: {e}")
            return False

    def load_hyperparameters(self):
        """
        Load the best hyperparameters from a file.
        """
        if os.path.exists(self.hyperparams_file):
            try:
                with open(self.hyperparams_file, 'rb') as f:
                    self.best_hyperparams = pickle.load(f)
                logger.info(f"Loaded hyperparameters from {self.hyperparams_file}")
                return True
            except Exception as e:
                logger.error(f"Error loading hyperparameters: {e}")
                self.best_hyperparams = {}
                return False
        else:
            logger.info("No saved hyperparameters found")
            self.best_hyperparams = {}
            return False

        # Set plot style
        # Use a valid style that works with newer matplotlib versions
        try:
            # Try newer style name format first
            plt.style.use('seaborn-v0_11-darkgrid')
        except:
            try:
                # Fall back to older style name if needed
                plt.style.use('seaborn')
            except:
                # If all else fails, use the default style
                logger.warning("Could not set custom style, using default")

        plt.rcParams['figure.figsize'] = [15, 8]

        logger.info("Initialized Storm Clusterer")

    def load_storms(self, storms_path=None):
        """
        Load storms from a pickle file.

        Parameters:
        -----------
        storms_path : str, optional
            Path to the pickle file. If None, uses the path provided at initialization.

        Returns:
        --------
        dict
            Dictionary of categorized storms.
        """
        if storms_path is not None:
            self.storms_path = storms_path

        if self.storms_path is None:
            logger.error("No storms path provided")
            return None

        logger.info(f"Loading storms from {self.storms_path}")

        try:
            with open(self.storms_path, 'rb') as f:
                self.categorized_storms = pickle.load(f)

            # Count total storms
            total_storms = sum(len(storms) for storms in self.categorized_storms.values())
            logger.info(f"Loaded {total_storms} storms across {len(self.categorized_storms)} categories")

            return self.categorized_storms
        except Exception as e:
            logger.error(f"Error loading storms: {e}")
            return None

    def preprocess_storms_gpu(self, data_arrays, time_arrays):
        """
        GPU-accelerated preprocessing of storm data arrays.

        Parameters:
        -----------
        data_arrays : list
            List of precipitation arrays
        time_arrays : list
            List of time arrays

        Returns:
        --------
        tuple
            (raw_data, normalized_data) as GPU arrays
        """
        if not USE_GPU:
            return None, None

        try:
            # Convert to GPU arrays with better memory management
            gpu_raw_data = []
            gpu_norm_data = []

            # Process in batches to avoid memory issues
            batch_size = min(100, len(data_arrays))  # Process in batches of 100

            for batch_start in range(0, len(data_arrays), batch_size):
                batch_end = min(batch_start + batch_size, len(data_arrays))
                batch_raw = []
                batch_norm = []

                for i in range(batch_start, batch_end):
                    precip = data_arrays[i]
                    times = time_arrays[i]

                    # Convert to GPU arrays with error checking
                    try:
                        gpu_precip = cp.asarray(precip, dtype=cp.float32)
                        gpu_times = cp.asarray(times, dtype=cp.float32)

                        # Validate GPU arrays
                        if gpu_times.size < 2 or gpu_precip.size < 2:
                            logger.warning(f"Insufficient data points in storm {i}, skipping")
                            continue

                        # Normalize time to [0, 1] range on GPU with safety checks
                        time_range = gpu_times[-1] - gpu_times[0]
                        if time_range <= 0:
                            logger.warning(f"Invalid time range for storm {i}, using default")
                            normalized_time = cp.linspace(0, 1, len(gpu_times), dtype=cp.float32)
                        else:
                            normalized_time = (gpu_times - gpu_times[0]) / time_range

                        # Normalize precipitation on GPU with safety checks
                        total_precip = gpu_precip[-1]
                        if total_precip <= 0:
                            logger.warning(f"Invalid precipitation total for storm {i}, using raw values")
                            normalized_precip = gpu_precip
                        else:
                            normalized_precip = gpu_precip / total_precip

                        # Create evenly spaced time points on GPU
                        time_points = cp.linspace(0, 1, self.resolution, dtype=cp.float32)

                        # Use GPU-accelerated interpolation (linear) with bounds checking
                        raw_interp = cp.interp(time_points, normalized_time, gpu_precip)
                        norm_interp = cp.interp(time_points, normalized_time, normalized_precip)

                        # Validate interpolated results
                        if cp.any(cp.isnan(raw_interp)) or cp.any(cp.isinf(raw_interp)):
                            logger.warning(f"Invalid raw interpolation for storm {i}, using zeros")
                            raw_interp = cp.zeros(self.resolution, dtype=cp.float32)

                        if cp.any(cp.isnan(norm_interp)) or cp.any(cp.isinf(norm_interp)):
                            logger.warning(f"Invalid normalized interpolation for storm {i}, using zeros")
                            norm_interp = cp.zeros(self.resolution, dtype=cp.float32)

                        batch_raw.append(raw_interp)
                        batch_norm.append(norm_interp)

                    except Exception as storm_error:
                        logger.warning(f"GPU processing failed for storm {i}: {storm_error}")
                        # Add default arrays for failed storms
                        batch_raw.append(cp.zeros(self.resolution, dtype=cp.float32))
                        batch_norm.append(cp.zeros(self.resolution, dtype=cp.float32))

                # Add batch results to main lists
                gpu_raw_data.extend(batch_raw)
                gpu_norm_data.extend(batch_norm)

                # Clear batch memory
                del batch_raw, batch_norm
                cp.get_default_memory_pool().free_all_blocks()

            if not gpu_raw_data or not gpu_norm_data:
                logger.warning("No valid GPU data processed")
                return None, None

            # Stack arrays on GPU with error handling
            try:
                gpu_raw_matrix = cp.stack(gpu_raw_data)
                gpu_norm_matrix = cp.stack(gpu_norm_data)

                # Convert back to CPU
                raw_data = cp.asnumpy(gpu_raw_matrix)
                normalized_data = cp.asnumpy(gpu_norm_matrix)

                # Clear GPU memory aggressively
                del gpu_raw_data, gpu_norm_data, gpu_raw_matrix, gpu_norm_matrix
                cp.get_default_memory_pool().free_all_blocks()
                cp.get_default_pinned_memory_pool().free_all_blocks()

                logger.info(f"Used GPU-accelerated preprocessing for {len(raw_data)} storms")
                return raw_data, normalized_data

            except Exception as stack_error:
                logger.warning(f"GPU stacking failed: {stack_error}")
                return None, None

        except Exception as e:
            logger.warning(f"GPU preprocessing failed: {e}. Falling back to CPU.")
            # Clear any remaining GPU memory
            if USE_GPU:
                try:
                    cp.get_default_memory_pool().free_all_blocks()
                    cp.get_default_pinned_memory_pool().free_all_blocks()
                except:
                    pass
            return None, None
    
    def _validate_data_for_clustering(self, data):
        """
        Validate data for clustering to prevent inf/NaN errors.

        Parameters:
        -----------
        data : numpy.ndarray
            Data to validate

        Returns:
        --------
        bool
            True if data is valid, False otherwise
        """
        if data is None or data.size == 0:
            logger.warning("Data is None or empty")
            return False

        if np.any(np.isinf(data)) or np.any(np.isnan(data)):
            logger.warning("Data contains inf or NaN values")
            return False

        # Check if all values are zero (more robust check)
        if np.all(data == 0):
            logger.warning("Data contains all zero values")
            return False

        # Check if data has sufficient variance for clustering
        if data.ndim > 1:
            # For 2D data, check variance across features
            data_variance = np.var(data, axis=0)
            if np.all(data_variance < 1e-12):
                logger.warning("Data has insufficient variance for clustering")
                return False
        else:
            # For 1D data, check overall variance
            if np.var(data) < 1e-12:
                logger.warning("Data has insufficient variance for clustering")
                return False

        return True

    def _clean_data_for_clustering(self, data):
        """
        Clean data by replacing inf/NaN values with safe alternatives.

        Parameters:
        -----------
        data : numpy.ndarray
            Data to clean

        Returns:
        --------
        numpy.ndarray
            Cleaned data
        """
        if data is None or data.size == 0:
            logger.warning("Cannot clean None or empty data")
            return np.array([])

        # Make a copy to avoid modifying original data
        cleaned_data = data.copy()

        # Replace inf values with large finite numbers
        cleaned_data = np.where(np.isinf(cleaned_data), np.sign(cleaned_data) * 1e6, cleaned_data)

        # Replace NaN values with the mean of non-NaN values, or zero if all are NaN
        if np.any(np.isnan(cleaned_data)):
            if cleaned_data.ndim > 1:
                # For 2D data, replace NaN values column-wise
                for col in range(cleaned_data.shape[1]):
                    col_data = cleaned_data[:, col]
                    if np.all(np.isnan(col_data)):
                        cleaned_data[:, col] = 0
                    else:
                        col_mean = np.nanmean(col_data)
                        cleaned_data[:, col] = np.where(np.isnan(col_data), col_mean, col_data)
            else:
                # For 1D data
                if np.all(np.isnan(cleaned_data)):
                    cleaned_data = np.zeros_like(cleaned_data)
                else:
                    data_mean = np.nanmean(cleaned_data)
                    cleaned_data = np.where(np.isnan(cleaned_data), data_mean, cleaned_data)

        # Add small regularization to prevent exact zeros only if all values are zero
        if np.all(cleaned_data == 0):
            logger.info("Adding small regularization to prevent all-zero data")
            cleaned_data = cleaned_data + np.random.normal(0, 1e-6, cleaned_data.shape)

        return cleaned_data

    def preprocess_storms(self, category=None):
        """
        Preprocess storm data for clustering.

        Parameters:
        -----------
        category : str, optional
            Storm category to process (e.g., '6h', '12h'). If None, processes all categories.

        Returns:
        --------
        dict
            Dictionary containing preprocessed storm data.
        """
        if self.categorized_storms is None:
            logger.error("No storms loaded")
            return None

        logger.info("Preprocessing storm data for clustering...")
        logger.info(f"Starting preprocessing for category {category}...")

        # Initialize processed data dictionary
        processed_data = {}

        # Determine categories to process
        categories = [category] if category else self.categorized_storms.keys()

        # Process each category
        for cat in categories:
            if cat not in self.categorized_storms:
                logger.warning(f"Category {cat} not found in storms data")
                continue

            storms_list = self.categorized_storms[cat]
            if not storms_list:
                logger.warning(f"No storms found in category {cat}")
                continue

            logger.info(f"Preprocessing {len(storms_list)} storms in category {cat}")

            # Create evenly spaced time points
            time_points = np.linspace(0, 1, self.resolution)

            # Try GPU-accelerated preprocessing first
            if USE_GPU and len(storms_list) > 50:  # Use GPU for larger datasets
                logger.info(f"Attempting GPU-accelerated preprocessing for {len(storms_list)} storms")

                # Prepare data for GPU processing
                data_arrays = []
                time_arrays = []

                for storm in storms_list:
                    # Extract timestamps and cumulative precipitation
                    timestamps = [dt.datetime.strptime(entry[0], "%Y-%m-%d %H:%M:%S") if isinstance(entry[0], str)
                                 else entry[0] for entry in storm]
                    cumulative_precip = [entry[1] for entry in storm]

                    # Validate precipitation data
                    if not cumulative_precip or all(p == 0 for p in cumulative_precip):
                        logger.warning(f"Storm has no precipitation data, skipping")
                        continue

                    # Calculate storm duration
                    start_time = timestamps[0]
                    end_time = timestamps[-1]
                    duration = (end_time - start_time).total_seconds()
                    total_precip = cumulative_precip[-1]
                    
                    # Validate precipitation data
                    if total_precip <= 0:
                        logger.warning(f"Storm has zero or negative total precipitation: {total_precip}. Skipping.")
                        continue

                    if duration <= 0:
                        logger.warning(f"Storm has zero or negative duration: {duration}. Skipping.")
                        continue

                    # Safe division for normalization
                    normalized_precip = [p / max(total_precip, 1e-12) for p in cumulative_precip]

                    # Normalize time to [0, 1] range
                    normalized_time = [(t - start_time).total_seconds() / duration for t in timestamps]
                    
                    # Validate interpolation inputs
                    if len(normalized_time) < 2 or len(normalized_precip) < 2:
                        logger.warning(f"Storm has insufficient data points. Skipping.")
                        continue

                    data_arrays.append(cumulative_precip)
                    time_arrays.append(normalized_time)

                # Try GPU preprocessing
                raw_data, normalized_data = self.preprocess_storms_gpu(data_arrays, time_arrays)

                if raw_data is not None and normalized_data is not None:
                    logger.info("Successfully used GPU-accelerated preprocessing")
                else:
                    logger.info("GPU preprocessing failed, falling back to CPU")
                    raw_data = None
                    normalized_data = None
            else:
                raw_data = None
                normalized_data = None

            # Fall back to CPU preprocessing if GPU failed or not used
            if raw_data is None or normalized_data is None:
                logger.info("Using CPU preprocessing")

                # Initialize arrays for processed data
                raw_data = []
                normalized_data = []

                # Process each storm
                for i, storm in enumerate(storms_list):
                    if (i + 1) % 100 == 0 or i == 0 or i == len(storms_list) - 1:
                         logger.info(f"  Preprocessing storm {i+1}/{len(storms_list)} in category {cat}")
                    
                    try:
                        # Extract timestamps and cumulative precipitation
                        timestamps = [dt.datetime.strptime(entry[0], "%Y-%m-%d %H:%M:%S") if isinstance(entry[0], str)
                                     else entry[0] for entry in storm]
                        cumulative_precip = [entry[1] for entry in storm]

                        # Validate precipitation data
                        if not cumulative_precip or all(p == 0 for p in cumulative_precip):
                            logger.warning(f"Storm {i+1} has no precipitation data, using default values")
                            # Create minimal valid data
                            raw_data.append(np.zeros(self.resolution))
                            normalized_data.append(np.zeros(self.resolution))
                            continue

                        # Calculate storm duration
                        start_time = timestamps[0]
                        end_time = timestamps[-1]
                        duration = (end_time - start_time).total_seconds()
                        
                        # Handle zero duration
                        if duration <= 0:
                            logger.warning(f"Storm {i+1} has zero or negative duration, using default values")
                            raw_data.append(np.zeros(self.resolution))
                            normalized_data.append(np.zeros(self.resolution))
                            continue

                        # Normalize time to [0, 1] range
                        normalized_time = [(t - start_time).total_seconds() / duration for t in timestamps]

                        # Normalize precipitation to [0, 1] range with safety check
                        total_precip = cumulative_precip[-1]
                        if total_precip <= 0:
                            logger.warning(f"Storm {i+1} has zero total precipitation, using raw values")
                            normalized_precip = cumulative_precip
                        else:
                            normalized_precip = [p / total_precip for p in cumulative_precip]

                        # Create interpolation functions with error handling
                        try:
                            raw_interp = interp1d(normalized_time, cumulative_precip, kind='linear',
                                                bounds_error=False, fill_value=(0, total_precip))
                            norm_interp = interp1d(normalized_time, normalized_precip, kind='linear',
                                                 bounds_error=False, fill_value=(0, 1))

                            # Create evenly spaced time points
                            time_points = np.linspace(0, 1, self.resolution)

                            # Interpolate precipitation values
                            raw_precip = raw_interp(time_points)
                            norm_precip = norm_interp(time_points)
                            
                            # Validate interpolated data
                            if np.any(np.isnan(raw_precip)) or np.any(np.isinf(raw_precip)):
                                logger.warning(f"Storm {i+1} produced invalid raw interpolation, using zeros")
                                raw_precip = np.zeros(self.resolution)
                                
                            if np.any(np.isnan(norm_precip)) or np.any(np.isinf(norm_precip)):
                                logger.warning(f"Storm {i+1} produced invalid normalized interpolation, using zeros")
                                norm_precip = np.zeros(self.resolution)

                            # Store processed data
                            raw_data.append(raw_precip)
                            normalized_data.append(norm_precip)
                            
                        except Exception as interp_error:
                            logger.warning(f"Interpolation failed for storm {i+1}: {interp_error}. Using default values.")
                            raw_data.append(np.zeros(self.resolution))
                            normalized_data.append(np.zeros(self.resolution))
                            
                    except Exception as storm_error:
                        logger.warning(f"Error processing storm {i+1}: {storm_error}. Using default values.")
                        raw_data.append(np.zeros(self.resolution))
                        normalized_data.append(np.zeros(self.resolution))

                # Convert to numpy arrays and validate
                raw_data = np.array(raw_data)
                normalized_data = np.array(normalized_data)
                
                # Final validation and cleaning
                raw_data = self._clean_data_for_clustering(raw_data)
                normalized_data = self._clean_data_for_clustering(normalized_data)

            # Store in processed data dictionary
            processed_data[cat] = {
                'raw': raw_data,
                'normalized': normalized_data,
                'time_points': time_points,
                'storms': storms_list
            }

            logger.info(f"Processed {len(raw_data)} storms in category {cat}")

        self.processed_data = processed_data
        return processed_data

    def find_optimal_clusters(self, category, data_type='normalized', method='silhouette'):
        """
        Find the optimal number of clusters for a given category.

        Parameters:
        -----------
        category : str
            Storm category to analyze.
        data_type : str
            Type of data to use ('raw' or 'normalized').
        method : str
            Method to use for determining optimal clusters ('silhouette', 'elbow', 'gap').

        Returns:
        --------
        int
            Optimal number of clusters.
        """
        if category not in self.processed_data:
            logger.error(f"Category {category} not found in processed data")
            return None

        data = self.processed_data[category][data_type]

        logger.info(f"Finding optimal number of clusters for {category} using {method} method...")

        # Initialize metrics
        silhouette_scores = []
        inertia_values = []
        ch_scores = []
        db_scores = []

        # Try different numbers of clusters
        for i, n_clusters in enumerate(self.n_clusters_range):
            logger.info(f"Testing {n_clusters} clusters ({i+1}/{len(self.n_clusters_range)})...")
            
            # Perform clustering with DTW distance (replacing softdtw since it's not supported)
            logger.info("Using DTW distance for optimal cluster determination (softdtw not supported in aeon)")
            try:
                # Use aeon's TimeSeriesKMeans with DTW distance
                from aeon.clustering import TimeSeriesKMeans
                dtw_km = TimeSeriesKMeans(
                    n_clusters=n_clusters,
                    distance="dtw",  # Using DTW instead of softdtw
                    random_state=42
                )
                
                # Ensure data is 3D for aeon clustering functions
                if data.ndim == 2:
                    data_3d = data.reshape(data.shape[0], data.shape[1], 1)
                else:
                    data_3d = data
                
                # Fit the model and get predictions
                y_pred = dtw_km.fit_predict(data_3d)
                
                # Calculate inertia manually since aeon might not provide it
                inertia = 0
                for i, point in enumerate(data):
                    cluster_center = dtw_km.cluster_centers_[y_pred[i]]
                    try:
                        # Use DTW distance
                        inertia += dtw_distance(point, cluster_center.flatten())
                    except ValueError:
                        # Fall back to Euclidean distance if DTW fails
                        inertia += np.sum((point - cluster_center.flatten()) ** 2)
                
            except Exception as e:
                logger.warning(f"Error using aeon's TimeSeriesKMeans with DTW: {e}. Falling back to tslearn with DTW.")
                # Fall back to tslearn's implementation with DTW
                from tslearn.clustering import TimeSeriesKMeans as TSLearnTimeSeriesKMeans
                dtw_km = TSLearnTimeSeriesKMeans(
                    n_clusters=n_clusters,
                    metric="dtw",
                    verbose=False,
                    random_state=42
                )
                
                # Fit the model and get predictions
                y_pred = dtw_km.fit_predict(data)
                inertia = dtw_km.inertia_

            # Calculate silhouette score
            sil_score = silhouette_score(data, y_pred, metric="euclidean")
            silhouette_scores.append(sil_score)

            # Calculate inertia (for elbow method)
            inertia_values.append(inertia)

            # Calculate Calinski-Harabasz Index
            ch_score = calinski_harabasz_score(data.reshape(data.shape[0], -1), y_pred)
            ch_scores.append(ch_score)

            # Calculate Davies-Bouldin Index
            db_score = davies_bouldin_score(data.reshape(data.shape[0], -1), y_pred)
            db_scores.append(db_score)

            # Calculate Rand Index and Adjusted Rand Index if ground truth is available
            # (This would require ground truth labels, which we don't have in this case)
            
            # Calculate Mutual Information metrics if ground truth is available
            # (This would require ground truth labels, which we don't have in this case)

            logger.info(f"  Silhouette score: {sil_score:.3f}")
            logger.info(f"  Inertia: {inertia:.3f}")
            logger.info(f"  Calinski-Harabasz score: {ch_score:.3f}")
            logger.info(f"  Davies-Bouldin score: {db_score:.3f}")

        # Determine optimal number of clusters based on the specified method
        if method == 'silhouette':
            # Higher silhouette score is better
            optimal_n_clusters = self.n_clusters_range[np.argmax(silhouette_scores)]
        elif method == 'elbow':
            # Find the elbow point in the inertia curve
            # This is a simple implementation - in practice, you might want a more sophisticated approach
            inertia_diffs = np.diff(inertia_values)
            inertia_diffs_2 = np.diff(inertia_diffs)
            optimal_idx = np.argmax(inertia_diffs_2) + 1
            optimal_n_clusters = self.n_clusters_range[optimal_idx]
        elif method == 'calinski_harabasz':
            # Higher CH score is better
            optimal_n_clusters = self.n_clusters_range[np.argmax(ch_scores)]
        elif method == 'davies_bouldin':
            # Lower DB score is better
            optimal_n_clusters = self.n_clusters_range[np.argmin(db_scores)]
        else:
            # Default to silhouette
            optimal_n_clusters = self.n_clusters_range[np.argmax(silhouette_scores)]

        logger.info(f"Optimal number of clusters for {category}: {optimal_n_clusters}")

        # Plot the metrics
        plt.figure(figsize=(15, 10))

        # Silhouette score
        plt.subplot(2, 2, 1)
        plt.plot(self.n_clusters_range, silhouette_scores, 'o-', color='blue')
        plt.axvline(x=optimal_n_clusters, color='red', linestyle='--')
        plt.title('Silhouette Score')
        plt.xlabel('Number of clusters')
        plt.ylabel('Score')
        plt.grid(True)

        # Inertia (for elbow method)
        plt.subplot(2, 2, 2)
        plt.plot(self.n_clusters_range, inertia_values, 'o-', color='green')
        plt.axvline(x=optimal_n_clusters, color='red', linestyle='--')
        plt.title('Inertia (Elbow Method)')
        plt.xlabel('Number of clusters')
        plt.ylabel('Inertia')
        plt.grid(True)

        # Calinski-Harabasz Index
        plt.subplot(2, 2, 3)
        plt.plot(self.n_clusters_range, ch_scores, 'o-', color='purple')
        plt.axvline(x=optimal_n_clusters, color='red', linestyle='--')
        plt.title('Calinski-Harabasz Index')
        plt.xlabel('Number of clusters')
        plt.ylabel('Score')
        plt.grid(True)

        # Davies-Bouldin Index
        plt.subplot(2, 2, 4)
        plt.plot(self.n_clusters_range, db_scores, 'o-', color='orange')
        plt.axvline(x=optimal_n_clusters, color='red', linestyle='--')
        plt.title('Davies-Bouldin Index')
        plt.xlabel('Number of clusters')
        plt.ylabel('Score')
        plt.grid(True)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f"{category}_cluster_metrics.png"))
        plt.close()

        return optimal_n_clusters

    def visualize_clusters(self, category, result_key, data_type='normalized'):
        """
        Visualize clustering results.

        Parameters:
        -----------
        category : str
            Storm category to visualize.
        result_key : str
            Key for the clustering result to visualize.
        data_type : str
            Type of data to use ('raw' or 'normalized').

        Returns:
        --------
        None
        """
        if category not in self.clustering_results or result_key not in self.clustering_results[category]:
            logger.error(f"Clustering result {result_key} for category {category} not found")
            return

        if category not in self.processed_data:
            logger.error(f"Processed data for category {category} not found")
            return

        # Get clustering results
        results = self.clustering_results[category][result_key]

        # Get data
        data = self.processed_data[category][data_type]
        time_points = self.processed_data[category]['time_points']

        # Get labels and centers
        labels = results['labels']
        centers = results['centers']

        # Check if centers is None
        if centers is None:
            logger.warning(f"No cluster centers found for {category} using {results['method']} method. Skipping visualization.")
            return

        # Get number of clusters
        n_clusters = len(set(labels))
        if -1 in labels:  # DBSCAN may have noise points with label -1
            n_clusters -= 1

        logger.info(f"Visualizing {n_clusters} clusters for {category} using {results['method']} method...")

        # Create figure for cluster visualization
        plt.figure(figsize=(15, 10))

        # Plot each cluster
        for i in range(n_clusters):
            # Get storms in this cluster
            cluster_mask = labels == i
            cluster_data = data[cluster_mask]

            # Skip empty clusters
            if len(cluster_data) == 0:
                continue

            # Create subplot
            plt.subplot(n_clusters, 1, i + 1)

            # Plot each storm in the cluster with light gray color
            for storm in cluster_data:
                plt.plot(time_points, storm, color='lightgray', alpha=0.3)

            # Plot cluster center in red
            if i < len(centers):
                # Ensure center has the right shape for plotting
                center = centers[i]
                if center.ndim > 1 and center.shape[0] == 1:
                    # If center has shape (1, n_timestamps), reshape to (n_timestamps,)
                    center = center.reshape(-1)
                plt.plot(time_points, center, color='red', linewidth=2)

            # Add title and labels
            plt.title(f"Cluster {i+1} ({len(cluster_data)} storms)")
            plt.xlabel("Normalized Time")

            if data_type == 'normalized':
                plt.ylabel("Normalized Cumulative Precipitation")
                plt.ylim(0, 1)
            else:
                plt.ylabel("Cumulative Precipitation (inches)")

            # Add grid
            plt.grid(True)

        # Add overall title
        plt.suptitle(f"{category} Storms - {results['method']} Clustering with {n_clusters} Clusters")

        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)

        # Save figure
        plt.savefig(os.path.join(self.output_dir, f"{category}_{results['method']}_{n_clusters}_clusters.png"))
        plt.close()

        # Create figure for all clusters in one plot
        plt.figure(figsize=(12, 8))

        # Define colors for clusters
        colors = plt.cm.tab10(np.linspace(0, 1, n_clusters))

        # Plot each cluster with different color
        for i in range(n_clusters):
            # Get storms in this cluster
            cluster_mask = labels == i
            cluster_data = data[cluster_mask]

            # Skip empty clusters
            if len(cluster_data) == 0:
                continue

            # Plot cluster center
            if i < len(centers):
                # Ensure center has the right shape for plotting
                center = centers[i]
                if center.ndim > 1 and center.shape[0] == 1:
                    # If center has shape (1, n_timestamps), reshape to (n_timestamps,)
                    center = center.reshape(-1)
                plt.plot(time_points, center, color=colors[i], linewidth=2, label=f"Cluster {i+1} ({len(cluster_data)} storms)")

        # Add title and labels
        plt.title(f"{category} Storms - {results['method']} Clustering with {n_clusters} Clusters")
        plt.xlabel("Normalized Time")

        if data_type == 'normalized':
            plt.ylabel("Normalized Cumulative Precipitation")
            plt.ylim(0, 1)
        else:
            plt.ylabel("Cumulative Precipitation (inches)")

        # Add legend
        plt.legend()

        # Add grid
        plt.grid(True)

        # Save figure
        plt.savefig(os.path.join(self.output_dir, f"{category}_{results['method']}_{n_clusters}_clusters_centers.png"))
        plt.close()

    def calculate_dtw_distance_matrix(self, data):
        """
        Calculate the DTW distance matrix for a set of time series.

        Parameters:
        -----------
        data : numpy.ndarray
            Time series data with shape (n_samples, n_timestamps).

        Returns:
        --------
        numpy.ndarray
            DTW distance matrix with shape (n_samples, n_samples).
        """
        global USE_GPU
        logger.info(f"Calculating DTW distance matrix for {len(data)} time series...")

        # Use aeon's pairwise distance function if available, otherwise fall back to tslearn
        if AEON_DTW_AVAILABLE:
            try:
                distance_matrix = pairwise_distance(data, metric=dtw_distance)
                logger.info("Used aeon's pairwise DTW distance calculation")
            except Exception as e:
                logger.warning(f"aeon DTW calculation failed: {e}. Falling back to manual calculation.")
                # Manual calculation using aeon's dtw_distance
                n_samples = data.shape[0]
                distance_matrix = np.zeros((n_samples, n_samples))
                for i in range(n_samples):
                    for j in range(i, n_samples):
                        dist = dtw_distance(data[i], data[j])
                        distance_matrix[i, j] = dist
                        distance_matrix[j, i] = dist
        else:
            # Fall back to tslearn's cdist_dtw
            distance_matrix = cdist_dtw(data)
            logger.info("Used tslearn's DTW distance calculation")

        if USE_GPU and not AEON_DTW_AVAILABLE:
            logger.info("Using GPU-accelerated DTW calculation")
            try:
                # Ensure data is on the GPU
                gpu_data = cp.asarray(data)

                # Get the length of the time series
                len_ts = data.shape[1]

                # Prepare the output array on the GPU
                gpu_distance_matrix = cp.zeros((len(data), len(data)), dtype=cp.float32)

                # Set the block and grid dimensions
                threads_per_block = min(len_ts, 1024)  # CUDA block size limit
                blocks_per_grid = 1

                # Launch the kernel for each pair of time series
                for i in range(len(data)):
                    for j in range(i, len(data)):
                        if i <= j:
                            # Prepare the output array for the pair
                            result = cp.zeros(1, dtype=cp.float32)

                            # Launch the kernel
                            dtw_kernel((blocks_per_grid,), (threads_per_block,),
                                       (gpu_data[i], gpu_data[j], result, len_ts))

                            # Store the result
                            gpu_distance_matrix[i, j] = result[0]
                            if i != j:
                                gpu_distance_matrix[j, i] = result[0]  # DTW is symmetric

                # Copy the distance matrix back to the CPU
                distance_matrix = cp.asnumpy(gpu_distance_matrix)
                logger.info("GPU DTW calculation completed successfully")
            except Exception as e:
                logger.error(f"GPU DTW calculation failed: {e}. Falling back to CPU.")
                USE_GPU = False
                # Fall back to the appropriate CPU method
                if AEON_DTW_AVAILABLE:
                    distance_matrix = pairwise_distance(data, metric=dtw_distance)
                else:
                    distance_matrix = cdist_dtw(data)

        return distance_matrix
        
    def calculate_msm_distance_matrix(self, data):
        """
        Calculate the MSM distance matrix for a set of time series.
        
        MSM (Move-Split-Merge) distance is prioritized based on research findings
        showing that distance measures that employ editing in conjunction with warping
        perform significantly better than other approaches.

        Parameters:
        -----------
        data : numpy.ndarray
            Time series data with shape (n_samples, n_timestamps).

        Returns:
        --------
        numpy.ndarray
            MSM distance matrix with shape (n_samples, n_samples).
        """
        global USE_GPU
        logger.info(f"Calculating MSM distance matrix for {len(data)} time series...")

        # Initialize distance matrix
        n_samples = data.shape[0]
        
        if USE_GPU:
            try:
                # Use GPU-accelerated pairwise distance calculation
                logger.info("Using GPU-accelerated MSM distance calculation")
                
                # Convert data to GPU
                gpu_data = cp.asarray(data, dtype=cp.float32)
                
                # Use aeon's msm_pairwise_distance with GPU arrays
                # Note: This may fall back to CPU for MSM, but data transfer is optimized
                distance_matrix = msm_pairwise_distance(cp.asnumpy(gpu_data))
                
                logger.info("GPU-accelerated MSM distance calculation completed")
                
            except Exception as e:
                logger.warning(f"GPU MSM calculation failed: {e}. Falling back to CPU with parallel processing.")
                USE_GPU = False
        
        if not USE_GPU:
            # Use CPU with parallel processing
            logger.info("Using CPU with parallel processing for MSM distance")
            
            # Use aeon's msm_pairwise_distance with parallel processing
            distance_matrix = msm_pairwise_distance(data)

        return distance_matrix

    def perform_clustering(self, category, data_type='normalized', n_clusters=None, method='softdtw', use_dtw=False):
        """
        Perform clustering on storm data.

        Parameters:
        -----------
        category : str
            Storm category to analyze.
        data_type : str
            Type of data to use ('raw' or 'normalized').
        n_clusters : int, optional
            Number of clusters. If None, uses the optimal number determined by find_optimal_clusters.
        method : str
            Clustering method to use ('softdtw', 'dtw', 'kmeans', 'dbscan', 'gmm', 'hierarchical', 'optics', 'spectral', 'hdbscan').
        use_dtw : bool, optional
            Whether to use DTW distance for clustering methods other than 'softdtw' and 'dtw'.

        Returns:
        --------
        dict
            Dictionary containing clustering results.
        """
        # Access the global USE_GPU variable
        global USE_GPU
        if category not in self.processed_data:
            logger.error(f"Category {category} not found in processed data")
            return None

        data = self.processed_data[category][data_type]

        # Determine number of clusters if not provided
        if n_clusters is None:
            n_clusters = self.find_optimal_clusters(category, data_type)

        logger.info(f"Performing {method} clustering on {category} with {n_clusters} clusters...")

        # Initialize results dictionary
        results = {
            'method': method,
            'n_clusters': n_clusters,
            'data_type': data_type,
            'labels': None,
            'centers': None,
            'silhouette_score': None,
            'inertia': None,
            'calinski_harabasz_score': None,
            'davies_bouldin_score': None
        }

        # Perform clustering based on the specified method
        if method == 'softdtw':
            # Use tslearn for Soft-DTW since aeon doesn't support it
            logger.info("Using tslearn's TimeSeriesKMeans with Soft-DTW (aeon doesn't support softdtw)")
            try:
                from tslearn.clustering import TimeSeriesKMeans as TSLearnTimeSeriesKMeans
                model = TSLearnTimeSeriesKMeans(
                    n_clusters=n_clusters,
                    metric="softdtw",
                    metric_params={"gamma": 0.01},
                    verbose=False,
                    random_state=42
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(data)
                results['centers'] = model.cluster_centers_
                results['inertia'] = model.inertia_

            except ImportError:
                logger.error("tslearn not available for Soft-DTW. Falling back to DTW with aeon.")
                # Fall back to DTW using aeon
                if data.ndim == 2:
                    data_3d = data.reshape(data.shape[0], data.shape[1], 1)
                else:
                    data_3d = data

                model = TimeSeriesKMeans(
                    n_clusters=n_clusters,
                    distance="dtw",
                    random_state=42
                )

                results['labels'] = model.fit_predict(data_3d)
                results['centers'] = model.cluster_centers_
                results['inertia'] = 0  # aeon might not provide inertia

        elif method == 'dtw':
            # DTW k-means using aeon's implementation with proper data format
            logger.info("Using aeon's TimeSeriesKMeans with DTW distance")
            try:
                # Validate data first
                if not self._validate_data_for_clustering(data):
                    data = self._clean_data_for_clustering(data)
                
                # Ensure 3D data format for aeon clustering
                if data.ndim == 2:
                    data_3d = data.reshape(data.shape[0], data.shape[1], 1)
                else:
                    data_3d = data
                
                # Try aeon's TimeSeriesKMeans with DTW
                model = TimeSeriesKMeans(
                    n_clusters=n_clusters,
                    distance="dtw",
                    random_state=42
                )
                
                # Fit the model and get predictions
                results['labels'] = model.fit_predict(data_3d)
                results['centers'] = model.cluster_centers_
                
                # Calculate inertia manually with proper data format for DTW distance
                inertia = 0
                for i, point in enumerate(data):
                    cluster_center = results['centers'][results['labels'][i]]
                    # Ensure proper 2D format for DTW distance calculation
                    if cluster_center.ndim == 3:
                        cluster_center = cluster_center.reshape(cluster_center.shape[1], cluster_center.shape[2])
                    if point.ndim == 1:
                        point = point.reshape(1, -1)
                    try:
                        inertia += dtw_distance(point, cluster_center)
                    except Exception as e:
                        logger.warning(f"DTW distance calculation failed: {e}. Using Euclidean fallback.")
                        inertia += np.sum((point.flatten() - cluster_center.flatten()) ** 2)
                results['inertia'] = inertia
                
            except Exception as e:
                logger.warning(f"Error using aeon's TimeSeriesKMeans with DTW: {e}. Falling back to tslearn.")
                # Fall back to tslearn's implementation if available
                try:
                    from tslearn.clustering import TimeSeriesKMeans as TSLearnTimeSeriesKMeans
                    model = TSLearnTimeSeriesKMeans(
                        n_clusters=n_clusters,
                        metric="dtw",
                        verbose=False,
                        random_state=42
                    )

                    # Fit the model and get predictions
                    results['labels'] = model.fit_predict(data)
                    results['centers'] = model.cluster_centers_
                    results['inertia'] = model.inertia_

                except ImportError:
                    logger.error("Both aeon and tslearn DTW clustering failed. Falling back to standard k-means.")
                    # Final fallback to standard k-means
                    flat_data = data.reshape(data.shape[0], -1)
                    model = KMeans(n_clusters=n_clusters, random_state=42)
                    results['labels'] = model.fit_predict(flat_data)
                    results['centers'] = model.cluster_centers_.reshape(n_clusters, data.shape[1])
                    results['inertia'] = model.inertia_

        elif method == 'kmeans':
            # Standard k-means (on flattened data)
            # Validate data first
            if not self._validate_data_for_clustering(data):
                logger.warning("Initial data validation failed for k-means clustering. Attempting to clean data.")
                # Try to clean the data first
                cleaned_data = self._clean_data_for_clustering(data)
                if not self._validate_data_for_clustering(cleaned_data):
                    logger.error("Data validation failed for k-means clustering even after cleaning")
                    # Return a default result structure instead of None
                    return {
                        'method': method,
                        'n_clusters': n_clusters,
                        'data_type': data_type,
                        'labels': np.zeros(data.shape[0], dtype=int),  # All points in cluster 0
                        'centers': np.zeros((1, data.shape[1])),  # Single zero center
                        'silhouette_score': -1.0,  # Invalid score
                        'inertia': float('inf'),
                        'calinski_harabasz_score': 0.0,
                        'davies_bouldin_score': float('inf')
                    }
                else:
                    data = cleaned_data

            # Flatten the data
            flat_data = data.reshape(data.shape[0], -1)

            # Clean data to prevent numerical issues
            flat_data = self._clean_data_for_clustering(flat_data)

            if USE_GPU:
                # Use GPU-accelerated preprocessing for KMeans
                try:
                    # Convert to GPU array with optimal memory management
                    gpu_data = cp.asarray(flat_data, dtype=cp.float32)
                    
                    # Use GPU for preprocessing, CPU for clustering
                    cpu_data = cp.asnumpy(gpu_data)
                    
                    # Create CPU model with optimized parameters
                    model = KMeans(
                        n_clusters=n_clusters,
                        random_state=42,
                        n_init='auto',  # Use auto to prevent memory issues
                        max_iter=300,
                        tol=1e-4,
                        algorithm='lloyd'
                    )
                    
                    # Fit the model and get predictions
                    results['labels'] = model.fit_predict(cpu_data)
                    results['centers'] = model.cluster_centers_.reshape(n_clusters, data.shape[1])
                    results['inertia'] = model.inertia_
                    
                    logger.info("Used GPU-accelerated data preprocessing with CPU KMeans")
                    
                    # Clear GPU memory aggressively to prevent leaks
                    del gpu_data, cpu_data
                    cp.get_default_memory_pool().free_all_blocks()
                    cp.get_default_pinned_memory_pool().free_all_blocks()
                    
                    # Force garbage collection
                    import gc
                    gc.collect()
                    
                except Exception as e:
                    logger.warning(f"GPU preprocessing failed: {e}. Falling back to CPU.")
                    USE_GPU = False

            if not USE_GPU:
                # Use CPU KMeans with optimized parameters and memory management
                import gc
                
                model = KMeans(
                    n_clusters=n_clusters,
                    random_state=42,
                    n_init='auto',  # Use auto to prevent memory issues
                    max_iter=300,
                    tol=1e-4,
                    algorithm='lloyd'  # Use Lloyd's algorithm for better performance
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(flat_data)

                # Reshape cluster centers back to time series format
                results['centers'] = model.cluster_centers_.reshape(n_clusters, data.shape[1])
                results['inertia'] = model.inertia_
                
                # Force garbage collection to prevent memory leaks
                del model
                gc.collect()

        elif method == 'dbscan':
            # DBSCAN
            # Note: DBSCAN doesn't require n_clusters, but we'll use it for consistency in results

            if use_dtw:
                # Calculate MSM distance matrix (prioritized based on research findings)
                logger.info("Using MSM distance for DBSCAN (prioritized based on research findings)")
                distance_matrix = self.calculate_msm_distance_matrix(data)

                # Determine eps parameter for MSM distances
                # Use a percentile of the distance distribution as eps
                eps = np.percentile(distance_matrix, 15)  # Use 15th percentile as eps

                logger.info(f"Using MSM distances with DBSCAN, eps={eps:.4f}")

                # We'll use CPU DBSCAN with precomputed distances
                model = DBSCAN(
                    eps=eps,
                    min_samples=max(5, len(data) // 20),
                    metric='precomputed',
                    n_jobs=N_JOBS
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(distance_matrix)

                logger.info("Used DBSCAN with MSM distances")
            else:
                # Flatten the data
                flat_data = data.reshape(data.shape[0], -1)

                # Determine eps parameter using nearest neighbors
                nn = NearestNeighbors(n_neighbors=min(10, len(flat_data)), n_jobs=N_JOBS)
                nn.fit(flat_data)
                distances, _ = nn.kneighbors(flat_data)
                distances = np.sort(distances[:, -1])

                # Find the elbow point for eps
                diffs = np.diff(distances)
                elbow_idx = np.argmax(diffs) + 1
                eps = distances[elbow_idx]

                if USE_GPU:
                    # Use GPU-accelerated preprocessing for DBSCAN
                    try:
                        # Convert to GPU array for preprocessing
                        gpu_data = cp.asarray(flat_data, dtype=cp.float32)
                        
                        # Use GPU for distance calculations if beneficial
                        # Convert back to CPU for sklearn DBSCAN (more stable)
                        cpu_data = cp.asnumpy(gpu_data)
                        
                        # Perform DBSCAN clustering on CPU with optimized parameters
                        model = DBSCAN(
                            eps=eps,
                            min_samples=max(5, len(flat_data) // 20),
                            metric='euclidean',
                            n_jobs=N_JOBS,
                            algorithm='auto'
                        )

                        # Fit the model and get predictions
                        results['labels'] = model.fit_predict(cpu_data)
                        logger.info("Used GPU-accelerated preprocessing with CPU DBSCAN")
                        
                        # Clear GPU memory
                        del gpu_data
                        cp.get_default_memory_pool().free_all_blocks()
                        
                    except Exception as e:
                        logger.warning(f"GPU preprocessing failed: {e}. Falling back to CPU.")
                        USE_GPU = False

                if not USE_GPU:
                    # Perform DBSCAN clustering on CPU with optimized parameters
                    model = DBSCAN(
                        eps=eps,
                        min_samples=max(5, len(flat_data) // 20),
                        metric='euclidean',
                        n_jobs=N_JOBS,  # Use specified number of CPU cores
                        algorithm='auto'  # Let sklearn choose the best algorithm
                    )

                    # Fit the model and get predictions
                    results['labels'] = model.fit_predict(flat_data)

            # Calculate cluster centers
            centers = []
            for label in set(results['labels']):
                if label != -1:  # Ignore noise points
                    mask = results['labels'] == label
                    center = data[mask].mean(axis=0)
                    centers.append(center)

            results['centers'] = np.array(centers)
            results['n_clusters'] = len(centers)

        elif method == 'spectral':
            # Spectral Clustering

            if use_dtw:
                # Calculate MSM distance matrix (prioritized based on research findings)
                logger.info("Using MSM distance for Spectral Clustering (prioritized based on research findings)")
                distance_matrix = self.calculate_msm_distance_matrix(data)

                # Convert distance to similarity (affinity)
                # Use a Gaussian kernel to convert distances to similarities
                sigma = np.median(distance_matrix)
                affinity_matrix = np.exp(-distance_matrix / (2 * sigma**2))

                # Perform spectral clustering with MSM-based affinity
                model = SpectralClustering(
                    n_clusters=n_clusters,
                    random_state=42,
                    n_jobs=N_JOBS,
                    affinity='precomputed'  # Use precomputed affinity matrix
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(affinity_matrix)
                
                logger.info("Used Spectral Clustering with MSM distances")
                
            # Calculate cluster centers
            centers = []
            for label in range(n_clusters):
                mask = results['labels'] == label
                if np.any(mask):
                    center = data[mask].mean(axis=0)
                    centers.append(center)

            results['centers'] = np.array(centers)

        elif method == 'hdbscan' and HDBSCAN_AVAILABLE:
            # HDBSCAN Clustering

            if use_dtw:
                # Calculate MSM distance matrix (prioritized based on research findings)
                logger.info("Using MSM distance for HDBSCAN (prioritized based on research findings)")
                distance_matrix = self.calculate_msm_distance_matrix(data)

                # Perform HDBSCAN clustering with MSM distances
                model = hdbscan.HDBSCAN(
                    min_cluster_size=max(5, len(data) // 20),
                    min_samples=max(5, len(data) // 30),
                    metric='precomputed',  # Use precomputed distances
                    core_dist_n_jobs=N_JOBS
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(distance_matrix)

                logger.info("Used HDBSCAN with MSM distances")
            else:
                # Flatten the data
                flat_data = data.reshape(data.shape[0], -1)

                # Perform HDBSCAN clustering with Euclidean distance
                model = hdbscan.HDBSCAN(
                    min_cluster_size=max(5, len(flat_data) // 20),
                    min_samples=max(5, len(flat_data) // 30),
                    metric='euclidean',
                    core_dist_n_jobs=N_JOBS
                )

                # Fit the model and get predictions
                results['labels'] = model.fit_predict(flat_data)

            # Calculate cluster centers
            centers = []
            for label in set(results['labels']):
                if label != -1:  # Ignore noise points
                    mask = results['labels'] == label
                    center = data[mask].mean(axis=0)
                    centers.append(center)

            results['centers'] = np.array(centers)
            results['n_clusters'] = len(centers)

        elif method == 'kmedoids':
            # Using aeon's TimeSeriesKMedoids implementation with MSM distance
            logger.info("Using aeon's TimeSeriesKMedoids with MSM distance (prioritized based on research)")
            try:
                # Try to use aeon's TimeSeriesKMedoids with MSM distance
                # Ensure data is 3D for aeon clustering functions
                if data.ndim == 2:
                    data_3d = data.reshape(data.shape[0], data.shape[1], 1)
                else:
                    data_3d = data
                
                model = TimeSeriesKMedoids(
                    n_clusters=n_clusters,
                    distance="msm",  # Using MSM distance based on research findings
                    random_state=42
                    # Removed n_jobs parameter as it's not supported in aeon 1.1.0
                )
                
                # Fit the model and get predictions
                results['labels'] = model.fit_predict(data_3d)
                results['centers'] = model.cluster_centers_
                
                # Calculate inertia manually
                inertia = 0
                for i, point in enumerate(data):
                    cluster_center = results['centers'][results['labels'][i]]
                    try:
                        # Try to use MSM distance
                        inertia += msm_distance(point, cluster_center)
                    except ValueError:
                        # Fall back to Euclidean distance if MSM fails
                        inertia += np.sum((point - cluster_center) ** 2)
                results['inertia'] = inertia
                
            except Exception as e:
                logger.warning(f"Error using aeon's TimeSeriesKMedoids: {e}. Falling back to TimeSeriesKMeans with DTW.")
                # Fall back to TimeSeriesKMeans with DTW since softdtw is not supported
                # Ensure data is 3D for aeon clustering functions
                if data.ndim == 2:
                    data_3d = data.reshape(data.shape[0], data.shape[1], 1)
                else:
                    data_3d = data
                
                model = TimeSeriesKMeans(
                    n_clusters=n_clusters,
                    distance="dtw",  # Use DTW instead of softdtw
                    random_state=42
                )
                
                # Fit the model and get predictions
                results['labels'] = model.fit_predict(data_3d)
                results['centers'] = model.cluster_centers_
                
                # Calculate inertia manually
                inertia = 0
                for i, point in enumerate(data):
                    cluster_center = results['centers'][results['labels'][i]]
                    try:
                        # Use DTW distance for consistency
                        inertia += dtw_distance(point, cluster_center.flatten())
                    except:
                        # Fall back to Euclidean distance if DTW fails
                        inertia += np.sum((point - cluster_center.flatten()) ** 2)
                results['inertia'] = inertia

        elif method == 'kspectralcentroid':
            # aeon KSpectralCentroid clustering
            logger.info("Using aeon KSpectralCentroid clustering")
            
            # Ensure data is 3D for aeon clustering functions
            if data.ndim == 2:
                data_3d = data.reshape(data.shape[0], data.shape[1], 1)
            else:
                data_3d = data
            
            model = KSpectralCentroid(
                n_clusters=n_clusters,
                random_state=42,
                n_init=10
                # KSpectralCentroid doesn't support distance parameter
            )

            # Fit the model and get predictions
            results['labels'] = model.fit_predict(data_3d)
            results['centers'] = model.cluster_centers_
            # KSpectralCentroid in aeon does not have inertia_
            
        elif method == 'kshape':
            # aeon KShape clustering
            logger.info("Using aeon KShape clustering")
            
            # Ensure data is 3D for aeon clustering functions
            if data.ndim == 2:
                data_3d = data.reshape(data.shape[0], data.shape[1], 1)
            else:
                data_3d = data
            
            model = KShape(
                n_clusters=n_clusters,
                random_state=42,
                n_init=10
                # Removed n_jobs parameter as it might not be supported in aeon 1.1.0
            )

            # Fit the model and get predictions
            results['labels'] = model.fit_predict(data_3d)
            results['centers'] = model.cluster_centers_
            # KShape does not have inertia_
            
        elif method == 'feature_based':
            # Feature-based clustering using basic statistical features
            logger.info("Using feature-based clustering with basic statistical features")
            
            # Extract features using basic statistical functions
            feature_functions = self.feature_extractors['basic_stats']['functions']
            
            # Initialize feature array
            n_samples = data.shape[0]
            n_features = len(feature_functions)
            features = np.zeros((n_samples, n_features))
            
            # Extract features for each time series
            for i, series in enumerate(data):
                for j, func in enumerate(feature_functions):
                    features[i, j] = func(series)
            
            # Apply standard KMeans to the extracted features
            if USE_GPU:
                try:
                    # Convert to GPU array for preprocessing
                    gpu_features = cp.asarray(features, dtype=cp.float32)
                    
                    # Use GPU for preprocessing, CPU for clustering
                    cpu_features = cp.asnumpy(gpu_features)
                    
                    # Use CPU KMeans with optimized parameters
                    model = KMeans(
                        n_clusters=n_clusters,
                        random_state=42,
                        n_init=10,
                        max_iter=300,
                        tol=1e-4,
                        algorithm='lloyd'
                    )
                    
                    # Fit the model and get predictions
                    results['labels'] = model.fit_predict(cpu_features)
                    results['inertia'] = model.inertia_
                    
                    logger.info("Used GPU-accelerated preprocessing for feature-based clustering")
                    
                    # Clear GPU memory
                    del gpu_features
                    cp.get_default_memory_pool().free_all_blocks()
                    
                except Exception as e:
                    logger.warning(f"GPU preprocessing failed: {e}. Falling back to CPU.")
                    USE_GPU = False
            
            if not USE_GPU:
                # Use CPU KMeans with optimized parameters
                model = KMeans(
                    n_clusters=n_clusters,
                    random_state=42,
                    n_init=10,
                    max_iter=300,
                    tol=1e-4,
                    algorithm='lloyd'
                )
                
                # Fit the model and get predictions
                results['labels'] = model.fit_predict(features)
                results['inertia'] = model.inertia_
            
            # Calculate cluster centers in the original time series space
            centers = []
            for label in range(n_clusters):
                mask = results['labels'] == label
                if np.any(mask):
                    center = data[mask].mean(axis=0)
                    centers.append(center)
            
            results['centers'] = np.array(centers)

        else:
            logger.error(f"Unknown clustering method: {method}")
            return None

        # Calculate evaluation metrics
        if len(set(results['labels'])) > 1:  # Only calculate if we have more than one cluster
            # Flatten data for some metrics
            flat_data = data.reshape(data.shape[0], -1)

            # Silhouette score
            results['silhouette_score'] = silhouette_score(flat_data, results['labels'], metric="euclidean")
            
            # Calinski-Harabasz Index
            results['calinski_harabasz_score'] = calinski_harabasz_score(flat_data, results['labels'])

            # Davies-Bouldin Index
            results['davies_bouldin_score'] = davies_bouldin_score(flat_data, results['labels'])
            
            # Calculate Purity score using our custom implementation
            # Note: This requires ground truth labels, which we don't have by default
            # results['purity_score'] = self.calculate_purity(ground_truth_labels, results['labels'])
            
            # The following metrics require ground truth labels, which we don't have by default
            # If ground truth labels are provided in the future, these can be calculated
            
            # Rand Index and Adjusted Rand Index
            # results['rand_score'] = rand_score(ground_truth_labels, results['labels'])
            # results['adjusted_rand_score'] = adjusted_rand_score(ground_truth_labels, results['labels'])
            
            # Mutual Information metrics
            # results['mutual_info_score'] = mutual_info_score(ground_truth_labels, results['labels'])
            # results['normalized_mutual_info_score'] = normalized_mutual_info_score(ground_truth_labels, results['labels'])
            # results['adjusted_mutual_info_score'] = adjusted_mutual_info_score(ground_truth_labels, results['labels'])

        # Check if silhouette score exists before formatting
        if 'silhouette_score' in results and results['silhouette_score'] is not None:
            logger.info(f"Clustering complete. Silhouette score: {results['silhouette_score']:.3f}")
        else:
            logger.info("Clustering complete. Silhouette score: Not available")

        # Store results
        if category not in self.clustering_results:
            self.clustering_results[category] = {}

        self.clustering_results[category][f"{method}_{n_clusters}"] = results

        return results

    def compare_clustering_methods(self, category, data_type='normalized', use_dtw=True):
        """
        Compare different clustering methods for a given category.

        Parameters:
        -----------
        category : str
            Storm category to analyze.
        data_type : str
            Type of data to use ('raw' or 'normalized').
        use_dtw : bool, optional
            Whether to use DTW distance for clustering methods. Default is True.

        Returns:
        --------
        dict
            Dictionary containing comparison results.
        """
        if category not in self.processed_data:
            logger.error(f"Category {category} not found in processed data")
            return None

        logger.info(f"Comparing clustering methods for {category}...")

        # Methods to compare
        methods = [
            'softdtw', 'dtw', 'kmeans', 'spectral', 'dbscan',
            'kmedoids', 'kspectralcentroid', 'kshape', 'feature_based'
        ]
        # Removed 'gmm', 'hierarchical', and 'optics' as they are not implemented
        
        logger.info(f"Comparing {len(methods)} methods...")

        # Add HDBSCAN if available
        if HDBSCAN_AVAILABLE:
            methods.append('hdbscan')

        # Find optimal number of clusters
        optimal_n_clusters = self.find_optimal_clusters(category, data_type)

        # Initialize comparison results
        comparison_results = {
            'optimal_n_clusters': optimal_n_clusters,
            'fixed_n_clusters': self.fixed_n_clusters,
            'methods': {}
        }

        # Perform clustering with each method using optimal number of clusters
        for i, method in enumerate(methods):
            logger.info(f"Comparing method {method} ({i+1}/{len(methods)})...")
            # Skip DTW for methods that already use it or don't need it
            method_use_dtw = use_dtw and method not in ['softdtw', 'dtw', 'kmedoids', 'kspectralcentroid', 'kshape', 'feature_based']

            logger.info(f"Performing {method} clustering with optimal {optimal_n_clusters} clusters...")
            if method_use_dtw:
                logger.info(f"Using DTW distances with {method}")
            optimal_results = self.perform_clustering(category, data_type, optimal_n_clusters, method, use_dtw=method_use_dtw)

            # Perform clustering with fixed number of clusters
            logger.info(f"Performing {method} clustering with fixed {self.fixed_n_clusters} clusters...")
            fixed_results = self.perform_clustering(category, data_type, self.fixed_n_clusters, method, use_dtw=method_use_dtw)

            # Check if clustering results are valid before storing
            if optimal_results is None:
                logger.error(f"Optimal clustering failed for method {method}. Skipping this method.")
                continue

            if fixed_results is None:
                logger.error(f"Fixed clustering failed for method {method}. Skipping this method.")
                continue

            # Store results with proper null checking
            comparison_results['methods'][method] = {
                'optimal': {
                    'n_clusters': optimal_n_clusters,
                    # Internal evaluation metrics (don't require ground truth)
                    'silhouette_score': optimal_results.get('silhouette_score'),
                    'calinski_harabasz_score': optimal_results.get('calinski_harabasz_score'),
                    'davies_bouldin_score': optimal_results.get('davies_bouldin_score'),

                    # External evaluation metrics (require ground truth)
                    # These will be None unless ground truth labels are provided
                    'rand_score': optimal_results.get('rand_score'),
                    'adjusted_rand_score': optimal_results.get('adjusted_rand_score'),
                    'mutual_info_score': optimal_results.get('mutual_info_score'),
                    'normalized_mutual_info_score': optimal_results.get('normalized_mutual_info_score'),
                    'adjusted_mutual_info_score': optimal_results.get('adjusted_mutual_info_score'),
                    'purity_score': optimal_results.get('purity_score')
                },
                'fixed': {
                    'n_clusters': self.fixed_n_clusters,
                    # Internal evaluation metrics (don't require ground truth)
                    'silhouette_score': fixed_results.get('silhouette_score'),
                    'calinski_harabasz_score': fixed_results.get('calinski_harabasz_score'),
                    'davies_bouldin_score': fixed_results.get('davies_bouldin_score'),

                    # External evaluation metrics (require ground truth)
                    # These will be None unless ground truth labels are provided
                    'rand_score': fixed_results.get('rand_score'),
                    'adjusted_rand_score': fixed_results.get('adjusted_rand_score'),
                    'mutual_info_score': fixed_results.get('mutual_info_score'),
                    'normalized_mutual_info_score': fixed_results.get('normalized_mutual_info_score'),
                    'adjusted_mutual_info_score': fixed_results.get('adjusted_mutual_info_score'),
                    'purity_score': fixed_results.get('purity_score')
                }
            }

            # Visualize results
            self.visualize_clusters(category, f"{method}_{optimal_n_clusters}", data_type)
            self.visualize_clusters(category, f"{method}_{self.fixed_n_clusters}", data_type)

        # Create comparison table with all metrics
        # First, create a structured DataFrame for optimal clusters
        optimal_metrics = {
            'method': [],
            'n_clusters': [],
            'silhouette_score': [],
            'calinski_harabasz_score': [],
            'davies_bouldin_score': [],
            'rand_score': [],
            'adjusted_rand_score': [],
            'mutual_info_score': [],
            'normalized_mutual_info_score': [],
            'adjusted_mutual_info_score': [],
            'purity_score': []
        }
        
        # Then create a structured DataFrame for fixed clusters
        fixed_metrics = {
            'method': [],
            'n_clusters': [],
            'silhouette_score': [],
            'calinski_harabasz_score': [],
            'davies_bouldin_score': [],
            'rand_score': [],
            'adjusted_rand_score': [],
            'mutual_info_score': [],
            'normalized_mutual_info_score': [],
            'adjusted_mutual_info_score': [],
            'purity_score': []
        }
        
        # Populate the DataFrames
        for method, results in comparison_results['methods'].items():
            # Optimal clusters
            optimal_metrics['method'].append(method)
            optimal_metrics['n_clusters'].append(results['optimal']['n_clusters'])
            optimal_metrics['silhouette_score'].append(results['optimal']['silhouette_score'])
            optimal_metrics['calinski_harabasz_score'].append(results['optimal']['calinski_harabasz_score'])
            optimal_metrics['davies_bouldin_score'].append(results['optimal']['davies_bouldin_score'])
            optimal_metrics['rand_score'].append(results['optimal'].get('rand_score'))
            optimal_metrics['adjusted_rand_score'].append(results['optimal'].get('adjusted_rand_score'))
            optimal_metrics['mutual_info_score'].append(results['optimal'].get('mutual_info_score'))
            optimal_metrics['normalized_mutual_info_score'].append(results['optimal'].get('normalized_mutual_info_score'))
            optimal_metrics['adjusted_mutual_info_score'].append(results['optimal'].get('adjusted_mutual_info_score'))
            optimal_metrics['purity_score'].append(results['optimal'].get('purity_score'))
            
            # Fixed clusters
            fixed_metrics['method'].append(method)
            fixed_metrics['n_clusters'].append(results['fixed']['n_clusters'])
            fixed_metrics['silhouette_score'].append(results['fixed']['silhouette_score'])
            fixed_metrics['calinski_harabasz_score'].append(results['fixed']['calinski_harabasz_score'])
            fixed_metrics['davies_bouldin_score'].append(results['fixed']['davies_bouldin_score'])
            fixed_metrics['rand_score'].append(results['fixed'].get('rand_score'))
            fixed_metrics['adjusted_rand_score'].append(results['fixed'].get('adjusted_rand_score'))
            fixed_metrics['mutual_info_score'].append(results['fixed'].get('mutual_info_score'))
            fixed_metrics['normalized_mutual_info_score'].append(results['fixed'].get('normalized_mutual_info_score'))
            fixed_metrics['adjusted_mutual_info_score'].append(results['fixed'].get('adjusted_mutual_info_score'))
            fixed_metrics['purity_score'].append(results['fixed'].get('purity_score'))
        
        # Create DataFrames
        optimal_df = pd.DataFrame(optimal_metrics)
        fixed_df = pd.DataFrame(fixed_metrics)
        
        # Save comparison tables
        optimal_df.to_csv(os.path.join(self.output_dir, f"{category}_optimal_clusters_comparison.csv"), index=False)
        fixed_df.to_csv(os.path.join(self.output_dir, f"{category}_fixed_clusters_comparison.csv"), index=False)
        
        # Create a combined table for visualization
        optimal_df['cluster_type'] = 'optimal'
        fixed_df['cluster_type'] = 'fixed'
        combined_df = pd.concat([optimal_df, fixed_df])
        combined_df.to_csv(os.path.join(self.output_dir, f"{category}_clustering_comparison.csv"), index=False)

        logger.info(f"Clustering comparison for {category} complete")

        return comparison_results

    def optimize_hyperparameters(self, category, data_type='normalized', force_reoptimize=False):
        """
        Optimize hyperparameters for clustering methods.

        Parameters:
        -----------
        category : str
            Storm category to analyze.
        data_type : str
            Type of data to use ('raw' or 'normalized').
        force_reoptimize : bool
            Whether to force re-optimization even if hyperparameters already exist.

        Returns:
        --------
        dict
            Dictionary containing optimization results.
        """
        global USE_GPU
        if category not in self.processed_data:
            logger.error(f"Category {category} not found in processed data")
            return None

        # Check if we already have optimized hyperparameters for this category
        if not force_reoptimize and category in self.best_hyperparams:
            logger.info(f"Using previously optimized hyperparameters for {category}")
            return self.best_hyperparams[category]

        data = self.processed_data[category][data_type]

        logger.info(f"Optimizing hyperparameters for {category}...")

        # Initialize results dictionary
        optimization_results = {}

        methods_to_optimize = ['softdtw', 'dbscan'] # Add other methods here if needed

        # Optimize each method
        for i, method in enumerate(methods_to_optimize):
            logger.info(f"Optimizing hyperparameters for {method} ({i+1}/{len(methods_to_optimize)})...")

            if method == 'softdtw':
                # Optimize Soft-DTW k-means using tslearn (aeon doesn't support softdtw)
                logger.info("Optimizing Soft-DTW k-means using tslearn (aeon doesn't support softdtw)...")

                # Define parameter grid
                param_grid = {
                    'n_clusters': list(range(2, 11)),
                    'gamma': [0.001, 0.01, 0.1, 1.0, 10.0]
                }

                # For hyperparameter optimization, we'll use a manual approach instead of GridSearchCV
                # since silhouette_score is not a built-in scorer for GridSearchCV
                logger.info("Using manual grid search for Soft-DTW hyperparameter optimization")

                # Use tslearn's implementation since aeon doesn't support softdtw
                try:
                    from tslearn.clustering import TimeSeriesKMeans as TSLearnTimeSeriesKMeans
                    from sklearn.metrics import silhouette_score
                    TSLEARN_AVAILABLE = True
                except ImportError:
                    logger.error("tslearn not available for Soft-DTW optimization. Skipping this method.")
                    TSLEARN_AVAILABLE = False

                if TSLEARN_AVAILABLE:
                    # Initialize results storage
                    best_score = -1
                    best_params = None
                    all_results = []

                    # Perform manual grid search
                    for n_clusters in param_grid['n_clusters']:
                        for gamma in param_grid['gamma']:
                            logger.info(f"Testing n_clusters={n_clusters}, gamma={gamma}")

                            # Create and fit model
                            model = TSLearnTimeSeriesKMeans(
                                n_clusters=n_clusters,
                                metric="softdtw",
                                metric_params={"gamma": gamma},
                                verbose=False,
                                random_state=42
                            )

                            # Fit the model
                            labels = model.fit_predict(data)

                            # Calculate silhouette score
                            try:
                                # Reshape data for silhouette score calculation
                                flat_data = data.reshape(data.shape[0], -1)
                                score = silhouette_score(flat_data, labels)
                            except Exception as e:
                                logger.warning(f"Error calculating silhouette score: {e}")
                                score = -1

                            # Store result
                            result = {
                                'n_clusters': n_clusters,
                                'gamma': gamma,
                                'score': score
                            }
                            all_results.append(result)

                            # Update best parameters if needed
                            if score > best_score:
                                best_score = score
                                best_params = {'n_clusters': n_clusters, 'gamma': gamma}

                            logger.info(f"  Silhouette score: {score:.3f}")

                    # Create a structure similar to GridSearchCV's results
                    grid_search_results = {
                        'best_params_': best_params,
                        'best_score_': best_score,
                        'cv_results_': all_results
                    }

                    # Store results
                    optimization_results['softdtw'] = {
                        'best_params': grid_search_results['best_params_'],
                        'best_score': grid_search_results['best_score_'],
                        'all_results': grid_search_results['cv_results_']
                    }

                    logger.info(f"Best Soft-DTW parameters: {optimization_results['softdtw']['best_params']}")
                    logger.info(f"Best Soft-DTW silhouette score: {optimization_results['softdtw']['best_score']:.3f}")
                else:
                    # Store empty results if tslearn is not available
                    optimization_results['softdtw'] = {
                        'best_params': {'n_clusters': 4, 'gamma': 0.01},  # Default params
                        'best_score': -1,
                        'all_results': []
                    }
                    logger.warning("Soft-DTW optimization skipped due to missing tslearn")

            elif method == 'dbscan':
                # Optimize DBSCAN
                logger.info("Optimizing DBSCAN...")

                # Flatten the data
                flat_data = data.reshape(data.shape[0], -1)

                # Define parameter grid
                param_grid = {
                    'eps': np.linspace(0.1, 2.0, 10),
                    'min_samples': [5, 10, 15, 20, 25]
                }

                # For hyperparameter optimization, we'll use a manual approach instead of GridSearchCV
                logger.info("Using manual grid search for DBSCAN hyperparameter optimization")
                
                from sklearn.metrics import silhouette_score
                
                # Initialize results storage
                best_score = -1
                best_params = None
                all_results = []
                
                # Perform manual grid search
                for eps in param_grid['eps']:
                    for min_samples in param_grid['min_samples']:
                        logger.info(f"Testing eps={eps:.2f}, min_samples={min_samples}")
                        
                        # Create and fit model
                        model = DBSCAN(
                            eps=eps,
                            min_samples=min_samples,
                            metric='euclidean',
                            n_jobs=N_JOBS
                        )
                        
                        # Fit the model
                        labels = model.fit_predict(flat_data)
                        
                        # Calculate silhouette score if there are at least 2 clusters
                        unique_labels = set(labels)
                        if len(unique_labels) > 1 and -1 not in unique_labels:
                            try:
                                score = silhouette_score(flat_data, labels)
                            except Exception as e:
                                logger.warning(f"Error calculating silhouette score: {e}")
                                score = -1
                        elif len(unique_labels) > 1:
                            # Handle case with noise points (-1 label)
                            non_noise_mask = labels != -1
                            if np.sum(non_noise_mask) > 1:
                                try:
                                    score = silhouette_score(flat_data[non_noise_mask], labels[non_noise_mask])
                                except Exception as e:
                                    logger.warning(f"Error calculating silhouette score: {e}")
                                    score = -1
                            else:
                                score = -1
                        else:
                            # Only one cluster or all noise points
                            score = -1
                        
                        # Store result
                        result = {
                            'eps': eps,
                            'min_samples': min_samples,
                            'score': score
                        }
                        all_results.append(result)
                        
                        # Update best parameters if needed
                        if score > best_score:
                            best_score = score
                            best_params = {'eps': eps, 'min_samples': min_samples}
                            
                        logger.info(f"  Silhouette score: {score:.3f}")
                
                # Create a structure similar to GridSearchCV's results
                grid_search_results = {
                    'best_params_': best_params,
                    'best_score_': best_score,
                    'cv_results_': all_results
                }

                # Store results
                optimization_results['dbscan'] = {
                    'best_params': grid_search_results['best_params_'],
                    'best_score': grid_search_results['best_score_'],
                    'all_results': grid_search_results['cv_results_']
                }

                logger.info(f"Best DBSCAN parameters: {optimization_results['dbscan']['best_params']}")
                logger.info(f"Best DBSCAN silhouette score: {optimization_results['dbscan']['best_score']:.3f}")

        # Plot optimization results
        self._plot_optimization_results(category, optimization_results)

        # Save the hyperparameters
        if category not in self.best_hyperparams:
            self.best_hyperparams[category] = {}

        self.best_hyperparams[category] = optimization_results
        self.save_hyperparameters()

        return optimization_results

    def _plot_optimization_results(self, category, optimization_results):
        """
        Plot hyperparameter optimization results.

        Parameters:
        -----------
        category : str
        Storm category.
        optimization_results : dict
        Dictionary containing optimization results.
        """
        # Plot Soft-DTW results
        if 'softdtw' in optimization_results and optimization_results['softdtw']['all_results']:
            softdtw_results = optimization_results['softdtw']['all_results']

            # Convert to DataFrame for easier plotting
            df = pd.DataFrame(softdtw_results)
            
            # Check if we're using the old GridSearchCV format or our new manual format
            if 'mean_test_score' in df.columns:
                # Original GridSearchCV format
                pivot = df.pivot_table(
                    index='param_gamma',
                    columns='param_n_clusters',
                    values='mean_test_score'
                )
            else:
                # Our manual grid search format
                pivot = pd.DataFrame(
                    [result for result in softdtw_results if isinstance(result, dict)]
                ).pivot_table(
                    index='gamma',
                    columns='n_clusters',
                    values='score'
                )

            # Plot heatmap
            plt.figure(figsize=(12, 8))
            plt.title(f"Soft-DTW Hyperparameter Optimization for {category}")
            plt.pcolormesh(pivot.columns, pivot.index, pivot.values, cmap='viridis')
            plt.colorbar(label='Silhouette Score (Worst -1 to 1 Best)')
            plt.xlabel('Number of Clusters')
            plt.ylabel('Gamma')
            plt.xscale('linear')
            plt.yscale('log')
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, f"{category}_softdtw_optimization.png"))
            plt.close()

        # Plot DBSCAN results
        if 'dbscan' in optimization_results and optimization_results['dbscan']['all_results']:
            dbscan_results = optimization_results['dbscan']['all_results']

            # Convert to DataFrame for easier plotting
            df = pd.DataFrame(dbscan_results)
            
            # Check if we're using the old GridSearchCV format or our new manual format
            if 'mean_test_score' in df.columns:
                # Original GridSearchCV format
                pivot = df.pivot_table(
                    index='param_min_samples',
                    columns='param_eps',
                    values='mean_test_score',
                    aggfunc='mean'  # Use mean in case of duplicate entries
                )
            else:
                # Our manual grid search format
                pivot = pd.DataFrame(
                    [result for result in dbscan_results if isinstance(result, dict)]
                ).pivot_table(
                    index='min_samples',
                    columns='eps',
                    values='score',
                    aggfunc='mean'  # Use mean in case of duplicate entries
                )

            # Plot heatmap
            plt.figure(figsize=(12, 8))
            plt.title(f"DBSCAN Hyperparameter Optimization for {category}")
            plt.pcolormesh(pivot.columns, pivot.index, pivot.values, cmap='viridis')
            plt.colorbar(label='Silhouette Score (Worst -1 to 1 Best)')
            plt.xlabel('Epsilon')
            plt.ylabel('Min Samples')
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, f"{category}_dbscan_optimization.png"))
            plt.close()

    def visualize_comparison_results(self, category):
        """
        Visualize clustering comparison results for a given category.
        
        This method creates visualizations for all evaluation metrics across different clustering methods.
        
        Parameters:
        -----------
        category : str
            Storm category to visualize.
            
        Returns:
        --------
        None
        """
        if category not in self.clustering_results:
            logger.error(f"No clustering results found for category {category}")
            return
            
        # Load the comparison results from CSV files
        optimal_csv = os.path.join(self.output_dir, f"{category}_optimal_clusters_comparison.csv")
        fixed_csv = os.path.join(self.output_dir, f"{category}_fixed_clusters_comparison.csv")
        
        if not os.path.exists(optimal_csv) or not os.path.exists(fixed_csv):
            logger.error(f"Comparison CSV files not found for category {category}")
            return
            
        optimal_df = pd.read_csv(optimal_csv)
        fixed_df = pd.read_csv(fixed_csv)
        
        # Create visualizations for internal metrics (don't require ground truth)
        internal_metrics = ['silhouette_score', 'calinski_harabasz_score', 'davies_bouldin_score']
        
        # Create a figure for internal metrics
        plt.figure(figsize=(15, 10))
        
        for i, metric in enumerate(internal_metrics):
            plt.subplot(2, 2, i+1)
            
            # For silhouette and calinski_harabasz, higher is better
            if metric in ['silhouette_score', 'calinski_harabasz_score']:
                # Sort by metric value in descending order
                sorted_df = optimal_df.sort_values(by=metric, ascending=False)
                plt.barh(sorted_df['method'], sorted_df[metric], color='blue')
                plt.title(f"{metric.replace('_', ' ').title()} (Higher is Better)")
            else:  # For davies_bouldin, lower is better
                # Sort by metric value in ascending order
                sorted_df = optimal_df.sort_values(by=metric, ascending=True)
                plt.barh(sorted_df['method'], sorted_df[metric], color='green')
                plt.title(f"{metric.replace('_', ' ').title()} (Lower is Better)")
                
            plt.xlabel('Score')
            plt.ylabel('Clustering Method')
            plt.grid(True, axis='x')
            
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, f"{category}_internal_metrics_comparison.png"))
        plt.close()
        
        # Check if external metrics (requiring ground truth) are available
        external_metrics = ['rand_score', 'adjusted_rand_score', 'mutual_info_score',
                           'normalized_mutual_info_score', 'adjusted_mutual_info_score', 'purity_score']
        
        # Check if any external metrics are available (not all None)
        if optimal_df[external_metrics].notna().any().any():
            # Create a figure for external metrics
            plt.figure(figsize=(15, 15))
            
            for i, metric in enumerate(external_metrics):
                plt.subplot(3, 2, i+1)
                
                # For all external metrics, higher is better
                # Filter out None values
                filtered_df = optimal_df[optimal_df[metric].notna()]
                
                if not filtered_df.empty:
                    # Sort by metric value in descending order
                    sorted_df = filtered_df.sort_values(by=metric, ascending=False)
                    plt.barh(sorted_df['method'], sorted_df[metric], color='purple')
                    plt.title(f"{metric.replace('_', ' ').title()} (Higher is Better)")
                    plt.xlabel('Score')
                    plt.ylabel('Clustering Method')
                    plt.grid(True, axis='x')
                else:
                    plt.text(0.5, 0.5, f"No {metric.replace('_', ' ').title()} data available",
                             horizontalalignment='center', verticalalignment='center',
                             transform=plt.gca().transAxes)
                    
            plt.tight_layout()
            plt.savefig(os.path.join(self.output_dir, f"{category}_external_metrics_comparison.png"))
            plt.close()
            
        # Create a radar chart comparing the top methods
        # Select top 3 methods based on silhouette score
        top_methods = optimal_df.sort_values(by='silhouette_score', ascending=False)['method'].head(3).tolist()
        
        # Create radar chart for internal metrics
        self._create_radar_chart(optimal_df, top_methods, internal_metrics,
                                category, "Internal Metrics",
                                os.path.join(self.output_dir, f"{category}_radar_internal.png"))
        
        # Create radar chart for external metrics if available
        if optimal_df[external_metrics].notna().any().any():
            # Filter metrics that have data
            available_metrics = [m for m in external_metrics if optimal_df[m].notna().any()]
            if available_metrics:
                self._create_radar_chart(optimal_df, top_methods, available_metrics,
                                        category, "External Metrics",
                                        os.path.join(self.output_dir, f"{category}_radar_external.png"))
    
    def _create_radar_chart(self, df, methods, metrics, category, title_suffix, save_path):
        """
        Create a radar chart for comparing clustering methods.
        
        Parameters:
        -----------
        df : pandas.DataFrame
            DataFrame containing the metrics data.
        methods : list
            List of methods to include in the radar chart.
        metrics : list
            List of metrics to include in the radar chart.
        category : str
            Storm category being visualized.
        title_suffix : str
            Suffix to add to the chart title.
        save_path : str
            Path to save the radar chart.
        """
        # Filter the DataFrame to include only the specified methods
        filtered_df = df[df['method'].isin(methods)]
        
        # Number of metrics
        N = len(metrics)
        
        # Create angles for the radar chart
        angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
        angles += angles[:1]  # Close the loop
        
        # Create figure
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True))
        
        # Add metric labels
        plt.xticks(angles[:-1], [m.replace('_', ' ').title() for m in metrics], size=12)
        
        # Plot each method
        colors = ['b', 'r', 'g', 'c', 'm', 'y']
        for i, method in enumerate(methods):
            # Get the method's data
            method_data = filtered_df[filtered_df['method'] == method]
            
            # For each metric, normalize the value to [0, 1] range
            values = []
            for metric in metrics:
                # Get all values for this metric
                all_values = df[metric].dropna()
                if len(all_values) == 0:
                    values.append(0)
                    continue
                    
                # Get min and max values
                min_val = all_values.min()
                max_val = all_values.max()
                
                # Get the method's value
                value = method_data[metric].values[0]
                
                # For davies_bouldin_score, lower is better, so invert the normalization
                if metric == 'davies_bouldin_score':
                    if max_val == min_val:
                        norm_value = 1.0  # Avoid division by zero
                    else:
                        norm_value = 1.0 - (value - min_val) / (max_val - min_val)
                else:
                    if max_val == min_val:
                        norm_value = 1.0  # Avoid division by zero
                    else:
                        norm_value = (value - min_val) / (max_val - min_val)
                
                values.append(norm_value)
            
            # Close the loop
            values += values[:1]
            
            # Plot the method
            ax.plot(angles, values, linewidth=2, linestyle='solid', label=method, color=colors[i % len(colors)])
            ax.fill(angles, values, alpha=0.1, color=colors[i % len(colors)])
        
        # Add legend
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
        
        # Add title
        plt.title(f"{category} Clustering Comparison - {title_suffix}", size=15)
        
        # Save the figure
        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()
    
    def process_all_categories(self, use_dtw=True):
        """
        Process all storm categories.

        Parameters:
        -----------
        use_dtw : bool, optional
        Whether to use DTW distance for clustering methods. Default is True.

        Returns:
        --------
        dict
        Dictionary containing results for all categories.
        """
        if self.categorized_storms is None:
            logger.error("No storms loaded")
            return None

        logger.info("Starting processing of all storm categories...")

        # Ask user if they want to redo hyperparameter optimization
        # Removed interactive input for hyperparameter optimization choice
        redo_optimization = 'n' # Defaulting to 'n' to avoid re-optimization in automated runs
        force_reoptimize = redo_optimization == 'y'

        # Initialize results dictionary
        all_results = {}

        # Process each category
        for i, category in enumerate(self.categorized_storms.keys()):
            logger.info(f"Processing category {category} ({i+1}/{len(self.categorized_storms)})...")

            # Preprocess storms
            self.preprocess_storms(category)

            # Compare clustering methods
            comparison_results = self.compare_clustering_methods(category, use_dtw=use_dtw)

            # Optimize hyperparameters
            optimization_results = self.optimize_hyperparameters(category, force_reoptimize=force_reoptimize)

            # Store results
            all_results[category] = {
                'comparison': comparison_results,
                'optimization': optimization_results
            }
            
            # Visualize comparison results
            self.visualize_comparison_results(category)

        logger.info("All categories processed")

        return all_results

    def calculate_purity(self, ground_truth_labels, cluster_labels):
        """
        Calculate the Purity score for clustering evaluation.

        Purity is a measure of the extent to which clusters contain a single class.
        For each cluster, it counts the number of data points from the most common class
        in that cluster and divides by the total number of data points.
        
        Parameters:
        -----------
        ground_truth_labels : array-like of shape (n_samples,)
            Ground truth class labels for each sample.
        cluster_labels : array-like of shape (n_samples,)
            Cluster labels assigned by the clustering algorithm.

        Returns:
        --------
        float
            The Purity score (between 0.0 and 1.0).
        """
        # Ensure labels are numpy arrays
        ground_truth_labels = np.asarray(ground_truth_labels)
        cluster_labels = np.asarray(cluster_labels)

        # Remove noise points if present (labeled as -1)
        non_noise_indices = cluster_labels != -1
        ground_truth_labels = ground_truth_labels[non_noise_indices]
        cluster_labels = cluster_labels[non_noise_indices]

        if len(np.unique(cluster_labels)) == 0:
            return 0.0 # No clusters formed (only noise or empty)

        # Calculate purity
        total_correct = 0
        for cluster_label in np.unique(cluster_labels):
            # Get ground truth labels for points in this cluster
            ground_truth_in_cluster = ground_truth_labels[cluster_labels == cluster_label]
            if ground_truth_in_cluster.size > 0:
                # Find the most frequent ground truth label in this cluster
                most_frequent_label = stats.mode(ground_truth_in_cluster)[0]
                # Count how many points in this cluster have the most frequent label
                correctly_classified = np.sum(ground_truth_in_cluster == most_frequent_label)
                total_correct += correctly_classified

        # Divide by the total number of data points (excluding noise)
        purity = total_correct / len(ground_truth_labels) if len(ground_truth_labels) > 0 else 0.0

        return purity
        
    def evaluate_clustering_with_ground_truth(self, labels_true, labels_pred):
        """
        Evaluate clustering results using ground truth labels.
        
        This method calculates all the evaluation metrics mentioned in the research paper:
        - Rand Index
        - Adjusted Rand Index
        - Mutual Information Score
        - Normalized Mutual Information
        - Adjusted Mutual Information
        - Silhouette Score (calculated on the data, not using ground truth)
        - Calinski-Harabasz Index (calculated on the data, not using ground truth)
        - Davies-Bouldin Index (calculated on the data, not using ground truth)
        - Purity Score
        
        Parameters:
        -----------
        labels_true : array-like of shape (n_samples,)
            Ground truth class labels for each sample.
        labels_pred : array-like of shape (n_samples,)
            Cluster labels assigned by the clustering algorithm.
            
        Returns:
        --------
        dict
            Dictionary containing all evaluation metrics.
        """
        # Ensure labels are numpy arrays
        labels_true = np.asarray(labels_true)
        labels_pred = np.asarray(labels_pred)
        
        # Remove noise points if present (labeled as -1)
        non_noise_indices = labels_pred != -1
        labels_true_filtered = labels_true[non_noise_indices]
        labels_pred_filtered = labels_pred[non_noise_indices]
        
        # Initialize results dictionary
        evaluation_results = {}
        
        # Calculate metrics that use ground truth labels
        evaluation_results['rand_score'] = rand_score(labels_true_filtered, labels_pred_filtered)
        evaluation_results['adjusted_rand_score'] = adjusted_rand_score(labels_true_filtered, labels_pred_filtered)
        evaluation_results['mutual_info_score'] = mutual_info_score(labels_true_filtered, labels_pred_filtered)
        evaluation_results['normalized_mutual_info_score'] = normalized_mutual_info_score(labels_true_filtered, labels_pred_filtered)
        evaluation_results['adjusted_mutual_info_score'] = adjusted_mutual_info_score(labels_true_filtered, labels_pred_filtered)
        evaluation_results['purity_score'] = self.calculate_purity(labels_true_filtered, labels_pred_filtered)
        
        # Log results
        logger.info("Clustering Evaluation Metrics with Ground Truth:")
        logger.info(f"  Rand Index: {evaluation_results['rand_score']:.3f}")
        logger.info(f"  Adjusted Rand Index: {evaluation_results['adjusted_rand_score']:.3f}")
        logger.info(f"  Mutual Information Score: {evaluation_results['mutual_info_score']:.3f}")
        logger.info(f"  Normalized Mutual Information: {evaluation_results['normalized_mutual_info_score']:.3f}")
        logger.info(f"  Adjusted Mutual Information: {evaluation_results['adjusted_mutual_info_score']:.3f}")
        logger.info(f"  Purity Score: {evaluation_results['purity_score']:.3f}")
        
        return evaluation_results

    def shift_invariant_average(self, data, max_iterations=100, tolerance=1e-6):
        """
        Compute shift-invariant average with numerical stability safeguards.
        
        This function addresses the LinAlgError by adding proper validation
        and fallback mechanisms for eigenvalue decomposition.
        
        Parameters:
        -----------
        data : numpy.ndarray
            Input time series data
        max_iterations : int
            Maximum iterations for convergence
        tolerance : float
            Convergence tolerance
            
        Returns:
        --------
        numpy.ndarray
            Shift-invariant average or fallback result
        """
        try:
            # Validate input data
            if not self._validate_data_for_clustering(data):
                logger.warning("Invalid data for shift_invariant_average, using simple mean")
                return np.mean(data, axis=0)
            
            # Clean data to remove inf/NaN values
            clean_data = self._clean_data_for_clustering(data)
            
            # Check if data has sufficient variation for eigenvalue decomposition
            data_std = np.std(clean_data, axis=0)
            if np.any(data_std < tolerance):
                logger.warning("Data has insufficient variation for shift-invariant average, using simple mean")
                return np.mean(clean_data, axis=0)
            
            # Attempt shift-invariant average computation with safeguards
            n_samples, n_timepoints = clean_data.shape
            
            # Create covariance matrix with regularization
            try:
                # Center the data
                centered_data = clean_data - np.mean(clean_data, axis=0)
                
                # Compute covariance matrix with regularization to prevent singularity
                cov_matrix = np.cov(centered_data.T)
                
                # Add regularization to diagonal to ensure numerical stability
                regularization = tolerance * np.eye(cov_matrix.shape[0])
                cov_matrix += regularization
                
                # Check condition number to ensure matrix is well-conditioned
                cond_num = np.linalg.cond(cov_matrix)
                if cond_num > 1e12:
                    logger.warning(f"Covariance matrix is ill-conditioned (cond={cond_num:.2e}), using simple mean")
                    return np.mean(clean_data, axis=0)
                
                # Compute eigenvalues and eigenvectors with error handling
                eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)
                
                # Check for invalid eigenvalues
                if np.any(np.isnan(eigenvals)) or np.any(np.isinf(eigenvals)) or np.any(eigenvals <= 0):
                    logger.warning("Invalid eigenvalues detected, using simple mean")
                    return np.mean(clean_data, axis=0)
                
                # Use the principal eigenvector for shift-invariant averaging
                principal_eigenvec = eigenvecs[:, -1]  # Last eigenvector (largest eigenvalue)
                
                # Compute weighted average using principal component
                weights = np.abs(principal_eigenvec)
                weights = weights / np.sum(weights)  # Normalize weights
                
                # Compute shift-invariant average
                shift_inv_avg = np.average(clean_data, axis=0, weights=weights)
                
                # Final validation of result
                if np.any(np.isnan(shift_inv_avg)) or np.any(np.isinf(shift_inv_avg)):
                    logger.warning("Shift-invariant average produced invalid values, using simple mean")
                    return np.mean(clean_data, axis=0)
                
                return shift_inv_avg
                
            except np.linalg.LinAlgError as linalg_error:
                logger.warning(f"Linear algebra error in shift_invariant_average: {linalg_error}. Using simple mean.")
                return np.mean(clean_data, axis=0)
                
        except Exception as e:
            logger.warning(f"Error in shift_invariant_average: {e}. Using simple mean.")
            return np.mean(data, axis=0) if data.size > 0 else np.zeros(data.shape[1:])

# Example usage
if __name__ == "__main__":
    # Initialize storm clusterer
    clusterer = StormClusterer(
        storms_path=os.path.join('results', 'storms.pkl'),
        output_dir='results'
    )

    # Load storms
    clusterer.load_storms()

    # Process all categories
    results = clusterer.process_all_categories()

    # Print summary
    logger.info("Clustering Analysis Summary:")
    for category, category_results in results.items():
        logger.info(f"Category: {category}")

        # Print comparison results
        comparison = category_results['comparison']
        logger.info(f"  Optimal number of clusters: {comparison['optimal_n_clusters']}")

        # Find best method based on silhouette score with optimal clusters
        best_method = max(
            comparison['methods'].keys(),
            key=lambda m: comparison['methods'][m]['optimal']['silhouette_score'] if comparison['methods'][m]['optimal']['silhouette_score'] is not None else -2 # Handle None case for silhouette score
        )

        logger.info(f"  Best clustering method: {best_method}")
        logger.info(f"  Best silhouette score: {comparison['methods'][best_method]['optimal']['silhouette_score']:.3f}" if comparison['methods'][best_method]['optimal']['silhouette_score'] is not None else "Best silhouette score: Not available")
