#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Storm Visualization

This script provides functions for visualizing storm data and clustering results.
It creates various plots including:
- Cumulative rainfall plots (actual and unitized)
- Cluster visualization
- Hyperparameter optimization results
- Comparison of clustering methods

Author: AI Assistant
"""

import os
import numpy as np
import pandas as pd
import pickle
import matplotlib.pyplot as plt
import matplotlib.ticker as mtick
import matplotlib.cm as cm
from matplotlib.colors import to_rgba
import datetime as dt
import logging
from collections import defaultdict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StormVisualizer:
    """Class to visualize storm data and clustering results."""
    
    def __init__(self, output_dir='results'):
        """
        Initialize the storm visualizer.
        
        Parameters:
        -----------
        output_dir : str
            Directory to store visualization outputs.
        """
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Set plot style
        # Use a valid style that works with newer matplotlib versions
        try:
            # Try newer style name format first
            plt.style.use('seaborn-v0_11-darkgrid')
        except:
            try:
                # Fall back to older style name if needed
                plt.style.use('seaborn')
            except:
                # If all else fails, use the default style
                logger.warning("Could not set custom style, using default")
        
        plt.rcParams['figure.figsize'] = [15, 8]
        
        # Define colors for plots
        self.colors = plt.cm.tab10(np.linspace(0, 1, 10))
        
        logger.info("Initialized Storm Visualizer")
    
    def plot_storm_category(self, category, storms, normalized=False, show_average=True):
        """
        Plot all storms in a category.
        
        Parameters:
        -----------
        category : str
            Storm category name.
        storms : list
            List of storm data.
        normalized : bool
            Whether to plot normalized (unitized) rainfall.
        show_average : bool
            Whether to show the average storm.
            
        Returns:
        --------
        str
            Path to the saved plot.
        """
        logger.info(f"Plotting {len(storms)} storms in category {category} (normalized={normalized})")
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Process storms for plotting
        processed_storms = []
        max_duration = 0
        
        for storm in storms:
            # Extract timestamps and cumulative precipitation
            timestamps = [dt.datetime.strptime(entry[0], "%Y-%m-%d %H:%M:%S") if isinstance(entry[0], str) 
                         else entry[0] for entry in storm]
            cumulative_precip = [entry[1] for entry in storm]
            
            # Calculate storm duration in hours
            start_time = timestamps[0]
            end_time = timestamps[-1]
            duration_hours = (end_time - start_time).total_seconds() / 3600
            
            if duration_hours > max_duration:
                max_duration = duration_hours
            
            # Create time points in hours from start
            time_hours = [(t - start_time).total_seconds() / 3600 for t in timestamps]
            
            # Store processed data
            if normalized:
                # Normalize precipitation to [0, 1] range
                total_precip = cumulative_precip[-1]
                norm_precip = [p / total_precip for p in cumulative_precip]
                processed_storms.append((time_hours, norm_precip))
            else:
                processed_storms.append((time_hours, cumulative_precip))
        
        # Plot each storm with light gray color
        for time_hours, precip in processed_storms:
            plt.plot(time_hours, precip, color='lightgray', alpha=0.3)
        
        # Calculate and plot average storm if requested
        if show_average and processed_storms:
            # Create common time axis
            common_time = np.linspace(0, max_duration, 100)
            
            # Interpolate each storm to common time axis
            from scipy.interpolate import interp1d
            
            interpolated_storms = []
            for time_hours, precip in processed_storms:
                # Create interpolation function
                if len(time_hours) > 1:
                    interp_func = interp1d(time_hours, precip, kind='linear', bounds_error=False, fill_value=(0, precip[-1]))
                    # Interpolate to common time axis
                    interpolated_precip = interp_func(common_time)
                    interpolated_storms.append(interpolated_precip)
            
            # Calculate average
            if interpolated_storms:
                avg_precip = np.mean(interpolated_storms, axis=0)
                
                # Plot average storm in red
                plt.plot(common_time, avg_precip, color='red', linewidth=2, label='Average Storm')
        
        # Add title and labels
        if normalized:
            plt.title(f"{category} Storms - Unitized Cumulative Rainfall")
            plt.ylabel("Normalized Cumulative Precipitation")
            plt.ylim(0, 1)
        else:
            plt.title(f"{category} Storms - Actual Cumulative Rainfall")
            plt.ylabel("Cumulative Precipitation (inches)")
        
        plt.xlabel("Time (hours)")
        
        # Add legend if showing average
        if show_average:
            plt.legend()
        
        # Add grid
        plt.grid(True)
        
        # Save figure
        filename = f"{category}_{'unitized' if normalized else 'actual'}_rainfall.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath)
        plt.close()
        
        logger.info(f"Saved plot to {filepath}")
        
        return filepath
    
    def plot_cluster_results(self, category, labels, centers, time_points, data, data_type='normalized'):
        """
        Plot clustering results.
        
        Parameters:
        -----------
        category : str
            Storm category name.
        labels : array
            Cluster labels for each storm.
        centers : array
            Cluster centers.
        time_points : array
            Time points for the data.
        data : array
            Storm data.
        data_type : str
            Type of data ('raw' or 'normalized').
            
        Returns:
        --------
        str
            Path to the saved plot.
        """
        # Get number of clusters
        n_clusters = len(set(labels))
        if -1 in labels:  # DBSCAN may have noise points with label -1
            n_clusters -= 1
        
        logger.info(f"Plotting {n_clusters} clusters for {category}")
        
        # Create figure
        plt.figure(figsize=(15, 10))
        
        # Plot each cluster
        for i in range(n_clusters):
            # Get storms in this cluster
            cluster_mask = labels == i
            cluster_data = data[cluster_mask]
            
            # Skip empty clusters
            if len(cluster_data) == 0:
                continue
            
            # Create subplot
            plt.subplot(n_clusters, 1, i + 1)
            
            # Plot each storm in the cluster with light gray color
            for storm in cluster_data:
                plt.plot(time_points, storm, color='lightgray', alpha=0.3)
            
            # Plot cluster center in red
            if i < len(centers):
                plt.plot(time_points, centers[i], color='red', linewidth=2)
            
            # Add title and labels
            plt.title(f"Cluster {i+1} ({len(cluster_data)} storms)")
            plt.xlabel("Normalized Time")
            
            if data_type == 'normalized':
                plt.ylabel("Normalized Cumulative Precipitation")
                plt.ylim(0, 1)
            else:
                plt.ylabel("Cumulative Precipitation (inches)")
            
            # Add grid
            plt.grid(True)
        
        # Add overall title
        plt.suptitle(f"{category} Storms - Clustering with {n_clusters} Clusters")
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save figure
        filename = f"{category}_{n_clusters}_clusters.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath)
        plt.close()
        
        logger.info(f"Saved cluster plot to {filepath}")
        
        # Create figure for all clusters in one plot
        plt.figure(figsize=(12, 8))
        
        # Define colors for clusters
        colors = plt.cm.tab10(np.linspace(0, 1, n_clusters))
        
        # Plot each cluster with different color
        for i in range(n_clusters):
            # Get storms in this cluster
            cluster_mask = labels == i
            cluster_data = data[cluster_mask]
            
            # Skip empty clusters
            if len(cluster_data) == 0:
                continue
            
            # Plot cluster center
            if i < len(centers):
                plt.plot(time_points, centers[i], color=colors[i], linewidth=2, label=f"Cluster {i+1} ({len(cluster_data)} storms)")
        
        # Add title and labels
        plt.title(f"{category} Storms - Cluster Centers")
        plt.xlabel("Normalized Time")
        
        if data_type == 'normalized':
            plt.ylabel("Normalized Cumulative Precipitation")
            plt.ylim(0, 1)
        else:
            plt.ylabel("Cumulative Precipitation (inches)")
        
        # Add legend
        plt.legend()
        
        # Add grid
        plt.grid(True)
        
        # Save figure
        filename = f"{category}_{n_clusters}_clusters_centers.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath)
        plt.close()
        
        logger.info(f"Saved cluster centers plot to {filepath}")
        
        return filepath
    
    def plot_hyperparameter_optimization(self, category, optimization_results):
        """
        Plot hyperparameter optimization results.
        
        Parameters:
        -----------
        category : str
            Storm category name.
        optimization_results : dict
            Dictionary containing optimization results.
            
        Returns:
        --------
        str
            Path to the saved plot.
        """
        logger.info(f"Plotting hyperparameter optimization results for {category}")
        
        # Create figure
        plt.figure(figsize=(15, 10))
        
        # Plot results for each method
        plot_idx = 1
        
        for method, results in optimization_results.items():
            if not results['all_results']:
                continue
            
            # Convert to DataFrame for easier plotting
            df = pd.DataFrame(results['all_results'])
            
            # Create subplot
            plt.subplot(2, 2, plot_idx)
            
            if method == 'softdtw':
                # Create pivot table for heatmap
                pivot = df.pivot_table(
                    index='gamma',
                    columns='n_clusters',
                    values='silhouette_score'
                )
                
                # Plot heatmap
                plt.pcolormesh(pivot.columns, pivot.index, pivot.values, cmap='viridis')
                plt.colorbar(label='Silhouette Score')
                plt.xlabel('Number of Clusters')
                plt.ylabel('Gamma')
                plt.xscale('linear')
                plt.yscale('log')
                plt.title(f'Soft-DTW Optimization')
                
            elif method == 'dbscan':
                # Create pivot table for heatmap
                pivot = df.pivot_table(
                    index='min_samples',
                    columns='eps',
                    values='silhouette_score'
                )
                
                # Plot heatmap
                plt.pcolormesh(pivot.columns, pivot.index, pivot.values, cmap='viridis')
                plt.colorbar(label='Silhouette Score')
                plt.xlabel('Epsilon')
                plt.ylabel('Min Samples')
                plt.title(f'DBSCAN Optimization')
            
            plot_idx += 1
        
        # Add overall title
        plt.suptitle(f"{category} Storms - Hyperparameter Optimization")
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save figure
        filename = f"{category}_hyperparameter_optimization.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath)
        plt.close()
        
        logger.info(f"Saved hyperparameter optimization plot to {filepath}")
        
        return filepath
    
    def plot_method_comparison(self, category, comparison_results):
        """
        Plot comparison of different clustering methods.
        
        Parameters:
        -----------
        category : str
            Storm category name.
        comparison_results : dict
            Dictionary containing comparison results.
            
        Returns:
        --------
        str
            Path to the saved plot.
        """
        logger.info(f"Plotting method comparison for {category}")
        
        # Create figure
        plt.figure(figsize=(15, 10))
        
        # Extract data for plotting
        methods = list(comparison_results['methods'].keys())
        optimal_n_clusters = comparison_results['optimal_n_clusters']
        fixed_n_clusters = comparison_results['fixed_n_clusters']
        
        # Prepare data for bar plots
        optimal_silhouette = [comparison_results['methods'][m]['optimal']['silhouette_score'] for m in methods]
        fixed_silhouette = [comparison_results['methods'][m]['fixed']['silhouette_score'] for m in methods]
        
        optimal_ch = [comparison_results['methods'][m]['optimal']['calinski_harabasz_score'] for m in methods]
        fixed_ch = [comparison_results['methods'][m]['fixed']['calinski_harabasz_score'] for m in methods]
        
        optimal_db = [comparison_results['methods'][m]['optimal']['davies_bouldin_score'] for m in methods]
        fixed_db = [comparison_results['methods'][m]['fixed']['davies_bouldin_score'] for m in methods]
        
        # Set up bar positions
        bar_width = 0.35
        r1 = np.arange(len(methods))
        r2 = [x + bar_width for x in r1]
        
        # Plot silhouette scores
        plt.subplot(3, 1, 1)
        plt.bar(r1, optimal_silhouette, width=bar_width, label=f'Optimal ({optimal_n_clusters} clusters)', color='blue')
        plt.bar(r2, fixed_silhouette, width=bar_width, label=f'Fixed ({fixed_n_clusters} clusters)', color='orange')
        plt.xlabel('Clustering Method')
        plt.ylabel('Silhouette Score')
        plt.title('Silhouette Score Comparison')
        plt.xticks([r + bar_width/2 for r in range(len(methods))], methods)
        plt.legend()
        
        # Plot Calinski-Harabasz scores
        plt.subplot(3, 1, 2)
        plt.bar(r1, optimal_ch, width=bar_width, label=f'Optimal ({optimal_n_clusters} clusters)', color='blue')
        plt.bar(r2, fixed_ch, width=bar_width, label=f'Fixed ({fixed_n_clusters} clusters)', color='orange')
        plt.xlabel('Clustering Method')
        plt.ylabel('Calinski-Harabasz Score')
        plt.title('Calinski-Harabasz Score Comparison')
        plt.xticks([r + bar_width/2 for r in range(len(methods))], methods)
        plt.legend()
        
        # Plot Davies-Bouldin scores
        plt.subplot(3, 1, 3)
        plt.bar(r1, optimal_db, width=bar_width, label=f'Optimal ({optimal_n_clusters} clusters)', color='blue')
        plt.bar(r2, fixed_db, width=bar_width, label=f'Fixed ({fixed_n_clusters} clusters)', color='orange')
        plt.xlabel('Clustering Method')
        plt.ylabel('Davies-Bouldin Score')
        plt.title('Davies-Bouldin Score Comparison (lower is better)')
        plt.xticks([r + bar_width/2 for r in range(len(methods))], methods)
        plt.legend()
        
        # Add overall title
        plt.suptitle(f"{category} Storms - Clustering Method Comparison")
        
        # Adjust layout
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # Save figure
        filename = f"{category}_method_comparison.png"
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath)
        plt.close()
        
        logger.info(f"Saved method comparison plot to {filepath}")
        
        return filepath

# Example usage
if __name__ == "__main__":
    # Initialize visualizer
    visualizer = StormVisualizer(output_dir='results')
    
    # Load storms from pickle file
    try:
        with open(os.path.join('results', 'storms.pkl'), 'rb') as f:
            storms = pickle.load(f)
        
        # Plot each category
        for category, storm_list in storms.items():
            # Plot actual rainfall
            visualizer.plot_storm_category(category, storm_list, normalized=False)
            
            # Plot unitized rainfall
            visualizer.plot_storm_category(category, storm_list, normalized=True)
        
        logger.info("Storm visualization complete. Plots saved to the results directory.")
    except Exception as e:
        logger.error(f"Error visualizing storms: {e}")
        logger.error(f"Error: {e}")