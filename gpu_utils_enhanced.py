#!/usr/bin/env python3
"""
Enhanced GPU Utilities for Storm Clustering
Provides multiple GPU acceleration options and robust fallbacks
"""

import os
import sys
import platform
import logging
import numpy as np
from typing import Optional, Tuple, Any

# Configure logging
logger = logging.getLogger(__name__)

class GPUAccelerator:
    """
    Enhanced GPU acceleration manager with multiple backends and robust fallbacks
    """
    
    def __init__(self):
        self.backend = None
        self.device_info = {}
        self.available_backends = []
        
        # Initialize GPU backends in order of preference
        self._init_cupy()
        self._init_pytorch()
        self._init_numba()
        
        # Set the best available backend
        self._select_backend()
        
    def _init_cupy(self):
        """Initialize CuPy backend with Windows DLL fix"""
        try:
            # Windows-specific DLL directory fix
            if platform.system() == "Windows":
                cuda_paths = [
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin",
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64",
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin",
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\lib\x64",
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\bin",
                    r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0\lib\x64"
                ]
                
                for cuda_path in cuda_paths:
                    if os.path.exists(cuda_path):
                        try:
                            os.add_dll_directory(cuda_path)
                            logger.debug(f"Added CUDA DLL directory: {cuda_path}")
                        except (OSError, AttributeError):
                            pass
            
            import cupy as cp
            
            # Test basic functionality
            test_array = cp.array([1, 2, 3])
            
            # Test NVRTC if possible
            nvrtc_available = False
            try:
                test_kernel = cp.RawKernel(r'''
                extern "C" __global__ void test() {}
                ''', 'test')
                nvrtc_available = True
            except Exception:
                pass
            
            # Get device info
            device = cp.cuda.Device()
            mempool = cp.get_default_memory_pool()
            
            self.device_info['cupy'] = {
                'version': cp.__version__,
                'cuda_version': cp.cuda.runtime.runtimeGetVersion(),
                'device_name': cp.cuda.runtime.getDeviceProperties(device.id)['name'].decode('utf-8'),
                'memory_total': mempool.total_bytes(),
                'nvrtc_available': nvrtc_available
            }
            
            self.available_backends.append('cupy')
            logger.info(f"✓ CuPy backend available (NVRTC: {'✓' if nvrtc_available else '✗'})")
            
        except Exception as e:
            logger.debug(f"CuPy backend not available: {e}")
    
    def _init_pytorch(self):
        """Initialize PyTorch backend as alternative"""
        try:
            import torch
            
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                device_name = torch.cuda.get_device_name(0)
                memory_total = torch.cuda.get_device_properties(0).total_memory
                
                self.device_info['pytorch'] = {
                    'version': torch.__version__,
                    'device_count': device_count,
                    'device_name': device_name,
                    'memory_total': memory_total
                }
                
                self.available_backends.append('pytorch')
                logger.info("✓ PyTorch CUDA backend available")
                
        except Exception as e:
            logger.debug(f"PyTorch backend not available: {e}")
    
    def _init_numba(self):
        """Initialize Numba CUDA backend"""
        try:
            from numba import cuda
            
            if cuda.is_available():
                device = cuda.get_current_device()
                
                self.device_info['numba'] = {
                    'device_name': device.name.decode('utf-8'),
                    'compute_capability': device.compute_capability,
                    'memory_total': device.memory_size
                }
                
                self.available_backends.append('numba')
                logger.info("✓ Numba CUDA backend available")
                
        except Exception as e:
            logger.debug(f"Numba backend not available: {e}")
    
    def _select_backend(self):
        """Select the best available backend"""
        if 'cupy' in self.available_backends:
            self.backend = 'cupy'
            logger.info("Selected CuPy as primary GPU backend")
        elif 'pytorch' in self.available_backends:
            self.backend = 'pytorch'
            logger.info("Selected PyTorch as primary GPU backend")
        elif 'numba' in self.available_backends:
            self.backend = 'numba'
            logger.info("Selected Numba as primary GPU backend")
        else:
            self.backend = None
            logger.info("No GPU backends available, using CPU only")
    
    def is_available(self) -> bool:
        """Check if GPU acceleration is available"""
        return self.backend is not None
    
    def get_backend(self) -> Optional[str]:
        """Get the current backend name"""
        return self.backend
    
    def get_device_info(self) -> dict:
        """Get device information for the current backend"""
        if self.backend and self.backend in self.device_info:
            return self.device_info[self.backend]
        return {}
    
    def to_gpu(self, array: np.ndarray) -> Any:
        """Convert numpy array to GPU array using current backend"""
        if not self.is_available():
            return array
        
        try:
            if self.backend == 'cupy':
                import cupy as cp
                return cp.asarray(array)
            elif self.backend == 'pytorch':
                import torch
                return torch.from_numpy(array).cuda()
            elif self.backend == 'numba':
                from numba import cuda
                return cuda.to_device(array)
        except Exception as e:
            logger.warning(f"Failed to move array to GPU: {e}")
            return array
        
        return array
    
    def to_cpu(self, gpu_array: Any) -> np.ndarray:
        """Convert GPU array back to numpy array"""
        if not self.is_available():
            return gpu_array
        
        try:
            if self.backend == 'cupy':
                import cupy as cp
                return cp.asnumpy(gpu_array)
            elif self.backend == 'pytorch':
                return gpu_array.cpu().numpy()
            elif self.backend == 'numba':
                return gpu_array.copy_to_host()
        except Exception as e:
            logger.warning(f"Failed to move array to CPU: {e}")
            return gpu_array
        
        return gpu_array
    
    def interpolate_gpu(self, x_new: np.ndarray, x: np.ndarray, y: np.ndarray) -> np.ndarray:
        """GPU-accelerated linear interpolation"""
        if not self.is_available():
            return np.interp(x_new, x, y)
        
        try:
            if self.backend == 'cupy':
                import cupy as cp
                gpu_x_new = cp.asarray(x_new)
                gpu_x = cp.asarray(x)
                gpu_y = cp.asarray(y)
                result = cp.interp(gpu_x_new, gpu_x, gpu_y)
                return cp.asnumpy(result)
            
            elif self.backend == 'pytorch':
                import torch
                import torch.nn.functional as F
                
                # PyTorch doesn't have direct interp, use manual implementation
                x_tensor = torch.from_numpy(x).cuda()
                y_tensor = torch.from_numpy(y).cuda()
                x_new_tensor = torch.from_numpy(x_new).cuda()
                
                # Simple linear interpolation implementation
                result = torch.zeros_like(x_new_tensor)
                for i, xi in enumerate(x_new_tensor):
                    # Find surrounding points
                    idx = torch.searchsorted(x_tensor, xi)
                    if idx == 0:
                        result[i] = y_tensor[0]
                    elif idx >= len(x_tensor):
                        result[i] = y_tensor[-1]
                    else:
                        # Linear interpolation
                        x0, x1 = x_tensor[idx-1], x_tensor[idx]
                        y0, y1 = y_tensor[idx-1], y_tensor[idx]
                        result[i] = y0 + (y1 - y0) * (xi - x0) / (x1 - x0)
                
                return result.cpu().numpy()
                
        except Exception as e:
            logger.warning(f"GPU interpolation failed: {e}, falling back to CPU")
            return np.interp(x_new, x, y)
        
        return np.interp(x_new, x, y)
    
    def clear_memory(self):
        """Clear GPU memory"""
        if not self.is_available():
            return
        
        try:
            if self.backend == 'cupy':
                import cupy as cp
                mempool = cp.get_default_memory_pool()
                mempool.free_all_blocks()
                pinned_mempool = cp.get_default_pinned_memory_pool()
                pinned_mempool.free_all_blocks()
            elif self.backend == 'pytorch':
                import torch
                torch.cuda.empty_cache()
            elif self.backend == 'numba':
                from numba import cuda
                cuda.current_context().memory_manager.deallocations.clear()
                
            logger.debug("GPU memory cleared")
            
        except Exception as e:
            logger.warning(f"Failed to clear GPU memory: {e}")

# Global GPU accelerator instance
gpu_accelerator = GPUAccelerator()

def get_gpu_info() -> dict:
    """Get GPU information"""
    return {
        'available': gpu_accelerator.is_available(),
        'backend': gpu_accelerator.get_backend(),
        'device_info': gpu_accelerator.get_device_info(),
        'available_backends': gpu_accelerator.available_backends
    }

def preprocess_storms_enhanced(data_arrays, time_arrays, resolution=100):
    """
    Enhanced GPU-accelerated preprocessing with multiple backend support
    """
    if not gpu_accelerator.is_available():
        logger.info("Using CPU preprocessing")
        return None, None
    
    try:
        logger.info(f"Using {gpu_accelerator.get_backend()} for GPU preprocessing")
        
        raw_data = []
        normalized_data = []
        
        for precip, times in zip(data_arrays, time_arrays):
            # Convert to GPU arrays
            gpu_precip = gpu_accelerator.to_gpu(np.array(precip, dtype=np.float32))
            gpu_times = gpu_accelerator.to_gpu(np.array(times, dtype=np.float32))
            
            # Normalize time to [0, 1] range
            time_range = times[-1] - times[0]
            normalized_time = (np.array(times) - times[0]) / time_range
            
            # Normalize precipitation
            total_precip = precip[-1]
            normalized_precip = np.array(precip) / total_precip
            
            # Create evenly spaced time points
            time_points = np.linspace(0, 1, resolution, dtype=np.float32)
            
            # Use GPU-accelerated interpolation
            raw_interp = gpu_accelerator.interpolate_gpu(time_points, normalized_time, precip)
            norm_interp = gpu_accelerator.interpolate_gpu(time_points, normalized_time, normalized_precip)
            
            raw_data.append(raw_interp)
            normalized_data.append(norm_interp)
        
        # Stack arrays
        raw_matrix = np.stack(raw_data)
        norm_matrix = np.stack(normalized_data)
        
        # Clear GPU memory
        gpu_accelerator.clear_memory()
        
        logger.info(f"GPU preprocessing completed using {gpu_accelerator.get_backend()}")
        return raw_matrix, norm_matrix
        
    except Exception as e:
        logger.warning(f"GPU preprocessing failed: {e}. Falling back to CPU.")
        gpu_accelerator.clear_memory()
        return None, None

if __name__ == "__main__":
    # Test the enhanced GPU utilities
    print("🔧 Enhanced GPU Utilities Test")
    print("=" * 40)
    
    info = get_gpu_info()
    print(f"GPU Available: {info['available']}")
    print(f"Backend: {info['backend']}")
    print(f"Available Backends: {info['available_backends']}")
    
    if info['available']:
        print(f"Device Info: {info['device_info']}")
        
        # Test basic operations
        test_data = np.random.random((100, 50))
        test_times = np.linspace(0, 1, 50)
        
        result = preprocess_storms_enhanced([test_data[0]], [test_times])
        if result[0] is not None:
            print("✓ Enhanced GPU preprocessing test successful!")
        else:
            print("✗ Enhanced GPU preprocessing test failed")
    else:
        print("No GPU acceleration available")