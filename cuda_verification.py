#!/usr/bin/env python3
"""
CUDA and CuPy Installation Verification Script
Comprehensive diagnostic tool for Windows CUDA/CuPy issues
"""

import os
import sys
import platform
import subprocess
import importlib.util

def check_system_info():
    """Check basic system information"""
    print("🖥️  SYSTEM INFORMATION")
    print("=" * 50)
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()[0]}")
    print(f"Python: {sys.version}")
    print()

def check_nvidia_driver():
    """Check NVIDIA driver and CUDA runtime version"""
    print("🎮 NVIDIA DRIVER & CUDA RUNTIME")
    print("=" * 50)
    
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Driver Version:' in line and 'CUDA Version:' in line:
                    print(f"✓ {line.strip()}")
                    break
        else:
            print("✗ nvidia-smi failed - NVIDIA driver may not be installed")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("✗ nvidia-smi not found - NVIDIA driver may not be installed")
    print()

def check_cuda_toolkit():
    """Check CUDA toolkit installations"""
    print("🔧 CUDA TOOLKIT INSTALLATIONS")
    print("=" * 50)
    
    cuda_base = r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA"
    if os.path.exists(cuda_base):
        versions = [d for d in os.listdir(cuda_base) if d.startswith('v')]
        versions.sort()
        
        for version in versions:
            version_path = os.path.join(cuda_base, version)
            bin_path = os.path.join(version_path, "bin")
            nvcc_path = os.path.join(bin_path, "nvcc.exe")
            nvrtc_path = os.path.join(bin_path, "nvrtc64_120_0.dll")
            
            print(f"📁 CUDA {version}:")
            print(f"   Path: {version_path}")
            print(f"   NVCC: {'✓' if os.path.exists(nvcc_path) else '✗'}")
            print(f"   NVRTC: {'✓' if os.path.exists(nvrtc_path) else '✗'}")
            
            # Check if in PATH
            current_path = os.environ.get('PATH', '')
            in_path = bin_path.lower() in current_path.lower()
            print(f"   In PATH: {'✓' if in_path else '✗'}")
            print()
    else:
        print("✗ CUDA toolkit directory not found")
    print()

def check_cupy_installation():
    """Check CuPy installation and compatibility"""
    print("🐍 CUPY INSTALLATION")
    print("=" * 50)
    
    try:
        import cupy as cp
        print(f"✓ CuPy version: {cp.__version__}")
        
        # Check CUDA version CuPy was compiled for
        cuda_version = cp.cuda.runtime.runtimeGetVersion()
        major = cuda_version // 1000
        minor = (cuda_version % 1000) // 10
        print(f"✓ CuPy compiled for CUDA: {major}.{minor}")
        
        # Test basic operations
        test_array = cp.array([1, 2, 3])
        print(f"✓ Basic operations: {cp.asnumpy(test_array)}")
        
        # Test GPU memory
        mempool = cp.get_default_memory_pool()
        print(f"✓ GPU memory pool initialized")
        
        return True
        
    except ImportError:
        print("✗ CuPy not installed")
        return False
    except Exception as e:
        print(f"✗ CuPy error: {e}")
        return False

def test_nvrtc_functionality():
    """Test NVRTC (runtime compilation) functionality"""
    print("⚡ NVRTC FUNCTIONALITY TEST")
    print("=" * 50)
    
    try:
        # Add CUDA directories to DLL search path
        if platform.system() == "Windows":
            cuda_paths = [
                r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin",
                r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64",
                r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\bin",
                r"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.8\lib\x64",
            ]
            
            for cuda_path in cuda_paths:
                if os.path.exists(cuda_path):
                    try:
                        os.add_dll_directory(cuda_path)
                        print(f"✓ Added DLL directory: {cuda_path}")
                    except (OSError, AttributeError):
                        pass
        
        import cupy as cp
        
        # Test NVRTC kernel compilation
        test_kernel = cp.RawKernel(r'''
        extern "C" __global__
        void test_kernel(float* data, int n) {
            int idx = blockIdx.x * blockDim.x + threadIdx.x;
            if (idx < n) {
                data[idx] = data[idx] * 2.0f;
            }
        }
        ''', 'test_kernel')
        
        # Test kernel execution
        data = cp.array([1.0, 2.0, 3.0, 4.0], dtype=cp.float32)
        test_kernel((1,), (4,), (data, len(data)))
        result = cp.asnumpy(data)
        
        print(f"✓ NVRTC kernel compilation successful")
        print(f"✓ Kernel execution result: {result}")
        print("✓ Full GPU acceleration available!")
        
        return True
        
    except Exception as e:
        print(f"✗ NVRTC test failed: {e}")
        print("⚠️  GPU acceleration limited to basic operations only")
        return False

def provide_solutions():
    """Provide solutions based on test results"""
    print("💡 SOLUTIONS & RECOMMENDATIONS")
    print("=" * 50)
    
    print("1. IMMEDIATE FIX (Already applied to storm_clustering.py):")
    print("   - Added Windows DLL directory fix")
    print("   - Should resolve NVRTC loading issues")
    print()
    
    print("2. PERMANENT PATH FIX:")
    print("   - Add CUDA 12.9 to system PATH permanently")
    print("   - Run as Administrator:")
    print('   [Environment]::SetEnvironmentVariable("PATH", "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;" + $env:PATH, "Machine")')
    print()
    
    print("3. ALTERNATIVE: Install correct CuPy version:")
    print("   pip uninstall cupy")
    print("   pip install cupy-cuda12x  # For CUDA 12.x")
    print()
    
    print("4. FALLBACK: CPU-only mode:")
    print("   - The code already has CPU fallbacks")
    print("   - Set USE_GPU = False to force CPU mode")
    print()

def main():
    """Main diagnostic function"""
    print("🔍 CUDA/CuPy Diagnostic Tool")
    print("=" * 50)
    print("Diagnosing CuPy NVRTC library loading issues...")
    print()
    
    # Run all checks
    check_system_info()
    check_nvidia_driver()
    check_cuda_toolkit()
    cupy_works = check_cupy_installation()
    nvrtc_works = test_nvrtc_functionality() if cupy_works else False
    
    print()
    provide_solutions()
    
    print()
    print("📊 FINAL STATUS")
    print("=" * 50)
    if nvrtc_works:
        print("🎉 SUCCESS: Full GPU acceleration available!")
        print("   Your storm_clustering.py should now work with GPU acceleration.")
    elif cupy_works:
        print("⚠️  PARTIAL: Basic GPU operations work, NVRTC limited")
        print("   GPU acceleration available for basic operations only.")
    else:
        print("❌ FAILED: GPU acceleration not available")
        print("   Will fall back to CPU-only processing.")
    
    return nvrtc_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)