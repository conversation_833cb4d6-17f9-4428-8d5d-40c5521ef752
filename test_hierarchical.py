#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for hierarchical clustering
"""

import numpy as np
from sklearn.cluster import AgglomerativeClustering

# Create some dummy data
data = np.random.rand(10, 5)
distance_matrix = np.random.rand(10, 10)

# Make the distance matrix symmetric
distance_matrix = (distance_matrix + distance_matrix.T) / 2
np.fill_diagonal(distance_matrix, 0)

print("Testing AgglomerativeClustering with metric='precomputed'...")
try:
    # Test with metric='precomputed'
    model = AgglomerativeClustering(
        n_clusters=2,
        metric='precomputed',
        linkage='average'
    )
    labels = model.fit_predict(distance_matrix)
    print("Success! Labels:", labels)
except Exception as e:
    print(f"Error with metric='precomputed': {e}")
    
    # Try with affinity='precomputed' as a fallback
    print("\nTrying with affinity='precomputed' instead...")
    try:
        model = AgglomerativeClustering(
            n_clusters=2,
            affinity='precomputed',
            linkage='average'
        )
        labels = model.fit_predict(distance_matrix)
        print("Success! Labels:", labels)
    except Exception as e:
        print(f"Error with affinity='precomputed': {e}")

print("\nTest complete.")