# STEP 5 of 6 – python_ml_docs tool with Hugging Face + PyTorch fallbacks
# STEP 4 of 6 – generic HTTP fetch fallback in docs_lookup
# STEP 1 of 6 – Project scaffolding
# STEP 6 of 6 – integrate chain-of-thought logging middleware via mcp-sequential-thinking
import sys
import os

# Add the mcp-sequential-thinking directory to the Python path
mcp_sequential_thinking_path = os.path.join(os.path.dirname(__file__), 'mcp-servers', 'mcp-sequential-thinking')
if os.path.exists(mcp_sequential_thinking_path):
    sys.path.append(mcp_sequential_thinking_path)

try:
    from mcp_sequential_thinking import SequentialThinkingMiddleware
except ImportError:
    # Define a dummy middleware class if the module is not available
    class SequentialThinkingMiddleware:
        def __init__(self, app):
            self.app = app
        
        async def __call__(self, scope, receive, send):
            await self.app(scope, receive, send)
import logging
try:
    import uvicorn
    from fastapi import <PERSON><PERSON><PERSON>, HTTPException
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    # Create dummy classes for FastAPI and HTTPException
    class FastAPI:
        def __init__(self, *args, **kwargs):
            pass
        
        def add_middleware(self, middleware):
            pass
        
        def get(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
    
    class HTTPException(Exception):
        def __init__(self, status_code=500, detail=""):
            self.status_code = status_code
            self.detail = detail
            super().__init__(f"{status_code}: {detail}")

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
import os
import json
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize FastAPI app if available
app = FastAPI() if FASTAPI_AVAILABLE else None

def run():
    """Programmatic entry point to start Uvicorn."""
    if FASTAPI_AVAILABLE:
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', '8000'))
        logger.info(f"Starting Uvicorn on {host}:{port}")
        uvicorn.run(app, host=host, port=port)
    else:
        logger.warning("FastAPI/Uvicorn not available. Server cannot be started.")
        print("FastAPI/Uvicorn not available. Server cannot be started.")

# STEP 2 of 6 – docs_lookup tool, FastAPI endpoint & Roo MCP binding
# Note: Removed 'from context7_upstash import Context7Client' as it seems incorrect;
# Context7 interaction should likely use MCP tools if needed later.


# Read progress.json and parse last_completed
with open('progress.json', 'r') as f:
    progress = json.load(f)
last_completed = progress.get('last_completed', 0)
# STEP 6 integration: add sequential thinking middleware and update progress
try:
    if FASTAPI_AVAILABLE:
        app.add_middleware(SequentialThinkingMiddleware)
    with open('progress.json', 'w') as f:
        json.dump({"last_completed": 6, "error": ""}, f)
except Exception as e:
    with open('progress.json', 'w') as f:
        json.dump({"last_completed": last_completed, "error": str(e)}, f)
    # Don't exit if we're just checking syntax
    if not any(arg in sys.argv for arg in ['-c', '--check']):
        logger.error(f"Failed to add middleware: {e}")

if last_completed < 3:
    async def docs_lookup_tool(input_data: dict) -> dict:
        # url = os.getenv('CONTEXT7_URL') # Context7 interaction removed, assuming MCP tool usage if needed
        # api_key = os.getenv('CONTEXT7_API_KEY') # Context7 interaction removed
        # client = Context7Client(url=url, api_key=api_key) # Context7 interaction removed
        query = input_data.get('query')
        # result = await client.lookup(query) # Context7 interaction removed
        chain_of_thought = []
        # chain_of_thought.append(f"Queried Context7 with query '{query}'") # Context7 interaction removed
        output = None # Initialize output as None since Context7 lookup is removed
        # Start fallback logic directly
        brave_url = os.getenv('BRAVE_SEARCH_URL')
        brave_api_key = os.getenv('BRAVE_SEARCH_API_KEY')
        if HTTPX_AVAILABLE:
            async with httpx.AsyncClient() as brave_client:
                resp = await brave_client.get(
                    brave_url,
                    params={'q': query},
                    headers={'Authorization': f'Bearer {brave_api_key}'},
                    timeout=httpx.Timeout(30.0, connect=5.0) # Added timeout
                )
        else:
            # Mock response when httpx is not available
            resp = type('MockResponse', (), {'status_code': 404, 'json': lambda: {'results': []}})
        chain_of_thought.append(f"Queried Brave Search with query '{query}'") # Updated CoT message
        if resp.status_code == 200:
                data = resp.json()
                results = data.get('results', [])
                fallback = []
                for item in results:
                    snippet = item.get('snippet')
                    url_item = item.get('url')
                    if snippet:
                        fallback.append(snippet)
                    elif url_item:
                        fallback.append(url_item)
                if fallback:
                    output = fallback
        if not output:
            generic_url = input_data.get("url")
            if not generic_url:
                generic_url = f"https://api.duckduckgo.com/?q={query}&format=json"
            if HTTPX_AVAILABLE:
                async with httpx.AsyncClient() as generic_client:
                    generic_resp = await generic_client.get(generic_url, timeout=httpx.Timeout(30.0, connect=5.0)) # Added timeout
            else:
                # Mock response when httpx is not available
                generic_resp = type('MockResponse', (), {'status_code': 404, 'text': '', 'json': lambda: {}})
            chain_of_thought.append(f"Brave Search returned no results; performed generic HTTP fetch to '{generic_url}'")
            if generic_resp.status_code == 200:
                try:
                    output = generic_resp.json()
                except ValueError:
                    output = generic_resp.text
        return {"output": output, "chain_of_thought": chain_of_thought}

@app.get("/mcp/run/docs_lookup") # Renamed endpoint path
async def mcp_run_docs_lookup(tool: str, input: str): # Renamed function
    try:
        input_json = json.loads(input)
        if tool == "docs_lookup":
            result = await docs_lookup_tool(input_json)
            # write progress
            out = {"last_completed": 4, "error": ""}
            with open('progress.json', 'w') as f2:
                json.dump(out, f2)
            return result
        else:
            # This specific endpoint only handles docs_lookup now
            logger.warning(f"Tool '{tool}' not handled by /mcp/run/docs_lookup endpoint.")
            raise HTTPException(status_code=404, detail=f"Tool '{tool}' not found at this endpoint")
    except Exception as e:
        logger.error(f"Error in /mcp/run/docs_lookup: {e}", exc_info=True) # Added logging
        err_out = {"last_completed": last_completed, "error": str(e)}
        try: # Prevent error during error handling from crashing server
            with open('progress.json', 'w') as f3:
                json.dump(err_out, f3)
        except Exception as dump_e:
             logger.error(f"Failed to write error progress to progress.json: {dump_e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}") # Replaced sys.exit
if last_completed < 5: # Keep conditional logic based on original progress steps
    async def python_ml_docs_tool(input_data: dict) -> dict:
        """
        Input: { "library": "<lib name>", "query": "<search text>" }
        Returns: { "output": ..., "chain_of_thought": [...] }
        """
        library = input_data.get("library")
        query = input_data.get("query")
        chain_of_thought = []
        output = None
        hf_endpoint = os.getenv("HF_INFERENCE_ENDPOINT")
        hf_api_key = os.getenv("HF_API_KEY")
        if HTTPX_AVAILABLE:
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {hf_api_key}"} if hf_api_key else {}
                resp = await client.post(hf_endpoint, headers=headers, json={"library": library, "query": query}, timeout=httpx.Timeout(30.0, connect=5.0)) # Added timeout
        else:
            # Mock response when httpx is not available
            resp = type('MockResponse', (), {'status_code': 404, 'json': lambda: {}})
        chain_of_thought.append(f"Queried Hugging Face docs API for library '{library}' with query '{query}'")
        if resp.status_code == 200:
            try:
                data = resp.json()
            except ValueError:
                data = None
            if data:
                output = data
        if not output:
            torch_url = f"https://pytorch.org/docs/{library}/search.html?query={query}"
            if HTTPX_AVAILABLE:
                async with httpx.AsyncClient() as client:
                    torch_resp = await client.get(torch_url, timeout=httpx.Timeout(30.0, connect=5.0)) # Added timeout
            else:
                # Mock response when httpx is not available
                torch_resp = type('MockResponse', (), {'status_code': 404, 'text': ''})
            chain_of_thought.append(f"Hugging Face docs API returned no results; fetched PyTorch docs at '{torch_url}'")
            if torch_resp.status_code == 200:
                import re
                matches = re.findall(r'<a href="([^"]+)"[^>]*>([^<]+)</a>', torch_resp.text)
                snippets = [f"{title}: {link}" for link, title in matches]
                output = snippets
        return {"output": output, "chain_of_thought": chain_of_thought}

    @app.get("/mcp/run/python_ml_docs") # Renamed endpoint path
    async def mcp_run_python_ml_docs(tool: str, input: str): # Renamed function
        try:
            input_json = json.loads(input)
            if tool == "python_ml_docs":
                result = await python_ml_docs_tool(input_json)
                out = {"last_completed": 5, "error": ""}
                with open('progress.json', 'w') as f2:
                    json.dump(out, f2)
                return result
            else:
                # This specific endpoint only handles python_ml_docs now
                logger.warning(f"Tool '{tool}' not handled by /mcp/run/python_ml_docs endpoint.")
                raise HTTPException(status_code=404, detail=f"Tool '{tool}' not found at this endpoint")
        except Exception as e:
            logger.error(f"Error in /mcp/run/python_ml_docs: {e}", exc_info=True) # Added logging
            err_out = {"last_completed": last_completed, "error": str(e)}
            try: # Prevent error during error handling from crashing server
                with open('progress.json', 'w') as f3:
                    json.dump(err_out, f3)
            except Exception as dump_e:
                 logger.error(f"Failed to write error progress to progress.json: {dump_e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}") # Replaced sys.exit

if __name__ == "__main__":
    # Only run the server if we're not just checking syntax
    if not any(arg in sys.argv for arg in ['-c', '--check']):
        run()
    else:
        print("Syntax check passed. No errors found.")