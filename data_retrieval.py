#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASOS Rainfall Data Retrieval for O'Hare (ORD)

This script retrieves precipitation data from the ASOS network for O'Hare International Airport.
It handles data downloading, parsing, and preprocessing for storm analysis.

Author: AI Assistant
"""

import os
import pandas as pd
import numpy as np
import datetime as dt
import requests
from io import StringIO
import logging
import csv
import time
import json
import httpx  # Added httpx import

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants for service URLs
ASOS_SERVICE_URL = "http://mesonet.agron.iastate.edu/cgi-bin/request/asos.py?"
HOURLY_PRECIP_SERVICE_URL = "https://mesonet.agron.iastate.edu/cgi-bin/request/hourlyprecip.py?"

class ASOSDataRetriever:
    """Class to retrieve and process ASOS precipitation data for O'Hare (ORD)."""
    
    def __init__(self, data_dir='data'):
        """
        Initialize the data retriever.

        Parameters:
        -----------
        data_dir : str
            Directory to store downloaded data.
        """
        self.station_id = 'ORD'  # O'Hare International Airport
        
        # Ensure data directory exists
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        logger.info(f"Initialized ASOS data retriever for {self.station_id}")
    
    def _download_data(self, uri):
        """
        Fetch the data from the IEM with exponential backoff.
        
        Parameters:
        -----------
        uri : str
            URL to fetch
            
        Returns:
        --------
        str
            Data from the URL
        """
        MAX_ATTEMPTS = 6
        attempt = 0
        while attempt < MAX_ATTEMPTS:
            try:
                logger.info(f"Attempting to download data from {uri} (attempt {attempt + 1}/{MAX_ATTEMPTS})")
                response = httpx.get(uri, timeout=300)
                response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
                data = response.text
                logger.info(f"Downloaded data from {uri}. First 100 characters: {data[:100]}")
                if data is not None and not data.startswith("ERROR"):
                    return data
            except httpx.RequestError as exc:
                logger.error(f"An error occurred while requesting {exc.request.url!r}: {exc}")
            except httpx.HTTPStatusError as exc:
                logger.error(f"Error response {exc.response.status_code} while requesting {exc.request.url!r}: {exc.response.text[:200]}")
            # Removed time.sleep(5)
            attempt += 1

        logger.error("Exhausted attempts to download, returning empty data")
        return ""
    
    def download_asos_data(self):
        """
        Download ASOS precipitation data for O'Hare.
        
        Returns:
        --------
        pandas.DataFrame
            DataFrame containing the precipitation data.
        """
        logger.info(f"Downloading all available ASOS data for {self.station_id}...")
        
        # Check if the data file exists locally first
        # local_file_path = os.path.join(self.data_dir, f"{self.station_id}_precip_data.csv")
        # logger.info(f"Checking for local data file: {local_file_path}")
        # if os.path.exists(local_file_path):
        #     logger.info(f"Loading data from local file: {local_file_path}")
        #     df = pd.read_csv(local_file_path, parse_dates=['valid'])

        # Download data from ASOS
        local_file_path = os.path.join(self.data_dir, f"{self.station_id}_precip_data.csv")
        try:
            # Initialize an empty DataFrame to store all data
            all_data = pd.DataFrame()

            # Base service URL - use HTTP instead of HTTPS as recommended in the sample code
            # Use the constant defined at the top
            SERVICE = ASOS_SERVICE_URL

            start_year = 2020
            end_year = 2025

            for year in range(start_year, end_year + 1):
                for month in range(1, 13):
                    for day in range(1, 32):
                        try:
                            date = dt.datetime(year, month, day)
                        except ValueError:
                            continue
                        # Construct the URL
                        uri = SERVICE + "data=all&tz=Etc/UTC&format=comma&latlon=yes&"
                        uri += f"year1={year}&month1={month:02}&day1={day:02}&"
                        uri += f"year2={year}&month2={month:02}&day2={day:02}&"
                        uri += f"station={self.station_id}"
                    # Download the data
                    data = self._download_data(uri)

                    if data:
                        # Save the raw response to a file for debugging
                        debug_file = os.path.join(self.data_dir, f"asos_raw_{year}_{month:02}.txt")
                        with open(debug_file, 'w') as f:
                            f.write(data)
                
                # Process the data
                try:
                    # Create a StringIO object to use with pandas
                    data_io = StringIO(data)
                    
                    # Find the header line
                    header_line = None
                    for line in data.split('\n'):
                        if 'station,valid' in line:
                            header_line = line
                            break
                    
                    if header_line:
                        # Skip lines until we reach the header
                        skip_rows = data.split('\n').index(header_line)
                        
                        # Read the CSV data
                        month_df = pd.read_csv(data_io, skiprows=skip_rows)
                        
                        # Rename columns if needed
                        if 'valid' in month_df.columns:
                            month_df.rename(columns={'valid': 'datetime'}, inplace=True)
                        if 'p01i' in month_df.columns:
                            month_df.rename(columns={'p01i': 'precipitation'}, inplace=True)
                        
                        # Convert datetime to proper format
                        month_df['datetime'] = pd.to_datetime(month_df['datetime'])
                        
                        # Convert precipitation to numeric, handling missing values
                        month_df['precipitation'] = pd.to_numeric(month_df['precipitation'], errors='coerce').fillna(0)
                        
                        # Keep only necessary columns
                        if 'datetime' in month_df.columns and 'precipitation' in month_df.columns:
                            month_df = month_df[['datetime', 'precipitation']]
                            
                            # Append to the main DataFrame
                            all_data = pd.concat([all_data, month_df], ignore_index=True)
                            
                            logger.info(f"Retrieved {len(month_df)} records")
                        else:
                            logger.warning(f"Required columns not found in data")
                    else:
                        logger.warning(f"Header line not found in data")
                except Exception as e:
                    logger.error(f"Error processing data: {e}")
            
            # Check if we got any data
            if len(all_data) > 0:
                # Remove duplicates
                all_data = all_data.drop_duplicates(subset=['datetime'])
                
                # Sort by datetime
                all_data = all_data.sort_values('datetime')
                
                # Save to our local directory
                os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
                all_data.to_csv(local_file_path, index=False)
                
                logger.info(f"Retrieved {len(all_data)} records from ASOS")
                return all_data
            else:
                logger.warning("No data retrieved from ASOS")
            
            # If we couldn't get data from ASOS, try local files as fallback
            # logger.info("Trying to use local data files as fallback...")
            # List of potential source files in order of preference
            # source_files = [
            #     'Data/Mesonet ORD Precip 1946-01 to 2022-10.txt',  # Try to find the earliest data file first
            #     'Data/Mesonet ORD Precip 2010-01 to 2022-10.txt',
            #     'MesonetORDPrecip2010-01t 2022-10.txt'
            # ]

            # Find the first available source file
            # available_files = []
            # for file_path in source_files:
            #     if os.path.exists(file_path):
            #         available_files.append(file_path)
            #         logger.info(f"Found data file: {file_path}")

            # if available_files:
            #     # Combine data from all available files
            #     dfs = []
            #     for file_path in available_files:
            #         logger.info(f"Reading data from: {file_path}")
            #         try:
            #             # Try different parsing approaches
            #             try:
            #                 temp_df = pd.read_csv(file_path, header=0)
            #             except:
            #                 # Try with tab delimiter if standard CSV parsing fails
            #                 temp_df = pd.read_csv(file_path, header=0, sep='\t')

            #         dfs.append(temp_df)
            #     except Exception as e:
            #         logger.warning(f"Error reading {file_path}: {e}")

            # if dfs:
            #     # Combine all dataframes
            #     df = pd.concat(dfs, ignore_index=True)

            #     # Remove duplicates if any
            #     if 'datetime' in df.columns:
            #         df = df.drop_duplicates(subset=['datetime'])

            #     # Save to our local directory
            #     os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
            #     df.to_csv(local_file_path, index=False)

            #     logger.info(f"Combined data from {len(dfs)} files with {len(df)} records. Returning data.")
            #     return df
            # else:
            logger.warning("No data could be read from available files. Creating sample data.")
            return self._create_sample_data(local_file_path)
                
        except Exception as e:
            logger.error(f"Error retrieving ASOS data: {e}")
            return self._create_sample_data(local_file_path)
    
    def _create_sample_data(self, save_path):
        """
        Create a sample dataset for demonstration purposes.
        
        Parameters:
        -----------
        save_path : str
            Path to save the sample data.
            
        Returns:
        --------
        pandas.DataFrame
            DataFrame containing the sample precipitation data.
        """
        logger.info("Creating sample precipitation data...")
        
        # Create date range
        date_range = pd.date_range(start='1940-01-01', end='2023-12-31', freq='H')
        
        # Create sample data with random precipitation values
        np.random.seed(42)  # For reproducibility
        
        # Most hours have no precipitation
        precip = np.zeros(len(date_range))
        
        # Add some random precipitation events (about 10% of hours)
        rain_indices = np.random.choice(
            len(date_range), 
            size=int(len(date_range) * 0.1), 
            replace=False
        )
        
        # Generate random precipitation amounts (mostly small, some larger)
        rain_amounts = np.random.exponential(scale=0.05, size=len(rain_indices))
        precip[rain_indices] = rain_amounts
        
        # Create DataFrame
        df = pd.DataFrame({
            'datetime': date_range,
            'precipitation': precip
        })
        
        # Save to file
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        df.to_csv(save_path, index=False)
        
        logger.info(f"Sample data created and saved to {save_path}")
        return df

    def download_hourly_precip_data(self, station, network, year1, month1, day1, year2, month2, day2, lalo=1, st=1, tz='America/Chicago'):
        """
        Download hourly precipitation data from the IEM.

        Parameters:
        -----------
        station : str
            Station ID.
        network : str
            Network ID.
        year1 : int
            Start year.
        month1 : int
            Start month.
        day1 : int
            Start day.
        year2 : int
            End year.
        month2 : int
            End month.
        day2 : int
            End day.
        lalo : int, optional
            Include latitude/longitude in output (default: 1).
        st : int, optional
            Include state in output (default: 1).
        tz : str, optional
            Timezone (default: 'America/Chicago').

        Returns:
        --------
        pandas.DataFrame
            DataFrame containing the hourly precipitation data.
        """
        logger.info(f"Downloading hourly precipitation data for {station} from IEM...")

        # Construct the URL
        # Use the constant defined at the top
        SERVICE = HOURLY_PRECIP_SERVICE_URL
        uri = SERVICE + f"network={network}&station={station}&year1={year1}&month1={month1}&day1={day1}&year2={year2}&month2={month2}&day2={day2}&lalo={lalo}&st={st}&tz={tz}"

        # Download the data
        data = self._download_data(uri)

        if data:
            # Parse the data
            try:
                data_io = StringIO(data)
                df = pd.read_csv(data_io)
                logger.info(f"Retrieved {len(df)} hourly precipitation records from IEM")
                return df
            except Exception as e:
                logger.error(f"Error parsing data: {e}")
                return None
        else:
            logger.warning("No data retrieved from IEM")
            return None
    def preprocess_data(self, df):
        """
        Preprocess the ASOS data for storm analysis.
        
        Parameters:
        -----------
        df : pandas.DataFrame
            Raw ASOS data.
            
        Returns:
        --------
        pandas.DataFrame
            Preprocessed data ready for storm analysis.
        """
        logger.info("Preprocessing ASOS data...")
        processed_df = pd.DataFrame()
        logger.info(f"Dataframe columns: {processed_df.columns}")
        # Make a copy to avoid modifying the original
        processed_df = df.copy()
        logger.info(f"Dataframe: {df}")
        
        # Ensure datetime column is in datetime format
        if 'datetime' not in processed_df.columns and 'valid' not in processed_df.columns:
            # Try to find a datetime column
            datetime_cols = [col for col in processed_df.columns if 'time' in col.lower() or 'date' in col.lower()]
            if datetime_cols:
                processed_df['datetime'] = pd.to_datetime(processed_df[datetime_cols[0]])
            else:
                logger.error("No datetime column found in the data")
                return None
        if 'valid' in processed_df.columns:
            processed_df['datetime'] = pd.to_datetime(processed_df['valid'])
            processed_df = processed_df.drop('valid', axis=1)
        
        # Ensure precipitation column exists
        precip_cols = [col for col in processed_df.columns if 'precip' in col.lower() or 'rain' in col.lower()]
        if not precip_cols and 'precip_in' in processed_df.columns:
            processed_df['precipitation'] = processed_df['precip_in']
        elif precip_cols:
            processed_df['precipitation'] = processed_df[precip_cols[0]]
        else:
            logger.error("No precipitation column found in the data")
            return None
        
        # Sort by datetime
        processed_df = processed_df.sort_values('datetime')
        
        # Resample to hourly data if needed
        logger.info("Resampling to hourly data...")
        processed_df = processed_df.set_index('datetime')
        processed_df = processed_df.resample('H').sum()
        processed_df = processed_df.reset_index()
        logger.info("Resampling complete.")
        # Keep only necessary columns
        cols_to_keep = ['datetime', 'precipitation']
        if 'lat' in processed_df.columns:
            cols_to_keep.append('lat')
        if 'lon' in processed_df.columns:
            cols_to_keep.append('lon')
        processed_df = processed_df[cols_to_keep]

        # Convert precipitation to inches if needed
        # (Assuming the data is already in inches, adjust if necessary)
        
        # Fill missing values with 0 (assuming no precipitation)
        processed_df['precipitation'] = processed_df['precipitation'].fillna(0)
        
        logger.info("Data preprocessing complete")
        return processed_df
    
    def get_precipitation_data(self):
        """
        Main method to retrieve and preprocess ASOS precipitation data.
        
        Returns:
        --------
        pandas.DataFrame
            Preprocessed precipitation data ready for storm analysis.
        """
        # Download hourly precipitation data from IEM
        iem_data = self.download_hourly_precip_data(
            station='ORD',
            network='IL_ASOS',
            year1=1946,
            month1=1,
            day1=1,
            year2=2025,
            month2=4,
            day2=4
        )

        # Preprocess data
        if iem_data is not None:
            processed_data = self.preprocess_data(iem_data)
            return processed_data
        else:
            return None

# Example usage
if __name__ == "__main__":
    # Initialize data retriever
    retriever = ASOSDataRetriever()
    
    # Get precipitation data
    precip_data = retriever.get_precipitation_data()
    
    # Display summary
    logger.info(f"Retrieved {len(precip_data)} hourly precipitation records")
    logger.info(f"Date range: {precip_data['datetime'].min()} to {precip_data['datetime'].max()}")
    logger.info(f"Total precipitation: {precip_data['precipitation'].sum():.2f} inches")
    logger.info(f"Maximum hourly precipitation: {precip_data['precipitation'].max():.2f} inches")
    
    # Save processed data
    output_file = os.path.join('NewCB', 'data', 'processed_precip_data.csv')
    precip_data.to_csv(output_file, index=False)
    logger.info(f"Processed data saved to {output_file}")